import { Effect, Option } from "effect";
import { describe, expect, it } from "vitest";

import type { PersonalizationMapping } from "../personalization-matching-effect";

import {
	mockBlueConicDialogs,
	mockEmptyPageState,
	mockPageState,
	mockPageStateNoMatch,
	mockPropositions,
} from "../__stubs__/personalization-matching-stubs";
import { personalizationMatchingEffect } from "../personalization-matching-effect";

describe("personalizationMatchingEffect", () => {
	// Test with empty components array
	it("should return Option.none() when no components are provided", async () => {
		const effect = personalizationMatchingEffect({
			components: mockEmptyPageState.components,
		});

		const result = await Effect.runPromise(effect);
		expect(result).toEqual(Option.none());
	});

	// Test with no matching BlueConic dialogs
	it("should return Option.none() when no matching BlueConic dialogs for blueconic-only", async () => {
		const effect = personalizationMatchingEffect({
			blueconicDialogs: [],
			components: mockPageStateNoMatch.components,
		});

		const result = await Effect.runPromise(effect);
		expect(result).toEqual(Option.none());
	});

	// Test with BlueConic dialogs only, no DGP
	it("should match BlueConic dialogs for blueconic-only and blueconic-first variants", async () => {
		const effect = personalizationMatchingEffect({
			blueconicDialogs: mockBlueConicDialogs,
			components: mockPageState.components,
			propositions: [],
		});

		const result = await Effect.runPromise(effect);
		expect(Option.isSome(result)).toBe(true);

		if (Option.isSome(result)) {
			const mappings = result.value.value;

			// The mappings will contain matches for BlueConic
			expect(mappings.length).toBeGreaterThan(0);

			// Check Banner component (blueconic-only)
			expect(mappings).toContainEqual({
				componentName: "Banner",
				id: "bc-dialog-1",
				position: 1,
				provider: "blueconic_dialogs",
			});

			// Check Promo component (blueconic-first)
			expect(mappings).toContainEqual({
				componentName: "Promo",
				id: "bc-dialog-2",
				position: 2,
				provider: "blueconic_dialogs",
			});

			// Make sure the non-relevant components are not included
			const nonRelevantComponents = mappings.filter(
				(m: PersonalizationMapping) => m.componentName === "Footer",
			);
			expect(nonRelevantComponents).toHaveLength(0);
		}
	});

	// Test with DGP propositions only, no BlueConic
	it("should match DGP propositions for dgp-only and dgp-first variants", async () => {
		const effect = personalizationMatchingEffect({
			blueconicDialogs: [],
			components: mockPageState.components,
			propositions: mockPropositions,
		});

		const result = await Effect.runPromise(effect);
		expect(Option.isSome(result)).toBe(true);

		if (Option.isSome(result)) {
			const mappings = result.value.value;

			// Should include DGP matches
			expect(mappings.length).toBeGreaterThan(0);

			// Verify that some components are using DGP as provider
			const dgpComponents = mappings.filter((m) => m.provider === "dgp");
			expect(dgpComponents.length).toBeGreaterThan(0);

			// Verify there are no blueconic_dialogs providers since we didn't provide any
			const blueconicComponents = mappings.filter(
				(m) => m.provider === "blueconic_dialogs",
			);
			expect(blueconicComponents).toHaveLength(0);

			// Make sure the non-relevant components are not included
			const footerComponent = mappings.find(
				(m) => m.componentName === "Footer",
			);
			expect(footerComponent).toBeUndefined();
		}
	});

	// Test BlueConic fallback for DGP-first
	it("should use BlueConic fallback for dgp-first when DGP proposition is not available", async () => {
		// Only provide one DGP proposition - not enough for both dgp components
		const limitedPropositions = mockPropositions.slice(0, 1);

		const effect = personalizationMatchingEffect({
			blueconicDialogs: mockBlueConicDialogs,
			components: mockPageState.components,
			propositions: limitedPropositions,
		});

		const result = await Effect.runPromise(effect);
		expect(Option.isSome(result)).toBe(true);

		if (Option.isSome(result)) {
			const mappings = result.value.value;

			// Check if Card component (dgp-first) fell back to BlueConic
			const cardMapping = mappings.find(
				(m) => m.componentName === "Card" && m.position === 4,
			);
			expect(cardMapping).toBeDefined();
			expect(cardMapping?.provider).toBe("blueconic_dialogs");
			expect(cardMapping?.id).toBe("bc-dialog-3");
		}
	});

	// Test DGP fallback for BlueConic-first
	it("should use DGP fallback for blueconic-first when BlueConic dialog is not available", async () => {
		// Remove Promo dialog from BlueConic dialogs
		const limitedDialogs = mockBlueConicDialogs.filter(
			(dialog) => dialog.componentName !== "Promo",
		);

		const effect = personalizationMatchingEffect({
			blueconicDialogs: limitedDialogs,
			components: mockPageState.components,
			propositions: mockPropositions,
		});

		const result = await Effect.runPromise(effect);
		expect(Option.isSome(result)).toBe(true);

		if (Option.isSome(result)) {
			const mappings = result.value.value;

			// Check if Promo component (blueconic-first) fell back to DGP
			const promoMapping = mappings.find(
				(m) => m.componentName === "Promo" && m.position === 2,
			);
			expect(promoMapping).toBeDefined();
			expect(promoMapping?.provider).toBe("dgp");
		}
	});

	// Test full integration with both BlueConic and DGP
	it("should handle all variant types correctly with both providers available", async () => {
		const effect = personalizationMatchingEffect({
			blueconicDialogs: mockBlueConicDialogs,
			components: mockPageState.components,
			propositions: mockPropositions,
		});

		const result = await Effect.runPromise(effect);
		expect(Option.isSome(result)).toBe(true);

		if (Option.isSome(result)) {
			const mappings = result.value.value;

			// Should have matches for all supported variant components
			expect(mappings.length).toBeGreaterThan(0);

			// BlueConic-only should use BlueConic
			expect(mappings).toContainEqual({
				componentName: "Banner",
				id: "bc-dialog-1",
				position: 1,
				provider: "blueconic_dialogs",
			});

			// BlueConic-first should use BlueConic
			expect(mappings).toContainEqual({
				componentName: "Promo",
				id: "bc-dialog-2",
				position: 2,
				provider: "blueconic_dialogs",
			});

			// Verify DGP components are present with appropriate IDs
			const sliderComponent = mappings.find(
				(m) => m.componentName === "Slider" && m.position === 3,
			);
			expect(sliderComponent).toBeDefined();
			expect(sliderComponent?.provider).toBe("dgp");

			const cardComponent = mappings.find(
				(m) => m.componentName === "Card" && m.position === 4,
			);
			expect(cardComponent).toBeDefined();
			expect(cardComponent?.provider).toBe("dgp");

			// Make sure the non-relevant component is not included
			const footerComponent = mappings.find(
				(m) => m.componentName === "Footer",
			);
			expect(footerComponent).toBeUndefined();
		}
	});
});

import type { Context } from "@netlify/functions";

import optimizely from "@optimizely/optimizely-sdk";
import { Effect, Option } from "effect";

import type { PageState } from "../schemas/page-state-schema";
import type { SombreroData } from "../schemas/sombrero-data-schema";

import { fetchOptimizelyDataFile } from "../helpers/fetch-optimizely-data-file";
import { getMultipleFeatureFlags } from "../helpers/get-multiple-feature-flags";

export type OptimizelyValue = {
	type: "optimizely";
	value: Record<string, boolean | string> | string;
};
const OPTIMIZATION_COOKIE_NAME = "sombrero-optimizely-user-id";
const SOMBRERO_AUTH_COOKIE_NAME = "AccessToken";

/**
 * Extracts user ID for Optimizely from headers or cookies
 *
 * Priority order:
 *
 * Sombrero-optimizely-user-id cookie
 *
 * @param cookies - Request cookies
 * @returns User ID string or undefined if not found
 */
const getUserId = (cookies: SombreroData["cookies"]) => {
	const cookie = cookies[OPTIMIZATION_COOKIE_NAME];

	if (cookie) return cookie;

	return undefined;
};

type OptimizelyEffect = {
	context: Context;
	pageState: PageState;
	request: Request;
	sombreroData: SombreroData;
};

/**
 * Effect that handles Optimizely feature flag evaluation and A/B testing
 *
 * This effect:
 *
 * 1. Extracts user ID and access token from request data
 * 2. Creates an Optimizely client instance with the latest datafile
 * 3. Sets up user context with geo, language, and authentication data
 * 4. Evaluates feature flags based on page state configuration
 * 5. Returns feature flag decisions or variation keys
 *
 * @param params - The parameters for the effect
 * @param params.context - Netlify function context
 * @param params.pageState - Page configuration including feature flag
 *   definitions
 * @param params.request - HTTP request object
 * @param params.sombreroData - Request data including cookies and headers
 * @returns Effect that resolves to Option containing Optimizely values or none
 *   if unavailable
 */
export const optimizelyEffect = ({
	pageState,
	request,
	sombreroData,
}: OptimizelyEffect) =>
	Effect.tryPromise({
		catch: (error) => `Optimizely Effect Error: ${error as Error}`,
		try: async () => {
			const userId = getUserId(sombreroData.cookies);

			if (!userId) {
				return Option.none();
			}

			const accessToken = sombreroData.cookies[SOMBRERO_AUTH_COOKIE_NAME];

			// Initialize Optimizely client with fresh datafile
			const client = optimizely.createInstance({
				datafile: await fetchOptimizelyDataFile(),
			});

			const headers = Object.fromEntries(request.headers.entries());

			// Create user context with relevant attributes for targeting
			const user = client?.createUserContext(userId, {
				acceptLanguage: headers["accept-language"] ?? "eu-NL",
				geo: headers["x-nf-geo"] ?? "NL", // Netlify geo header
				ip: headers["client-ip"] ?? headers["x-nf-client-connection-ip"] ?? "",
				isLoggedIn: !!accessToken,
				userAgent: headers["user-agent"] ?? "",
				userId,
			});

			if (!client || !user) {
				return Option.none();
			}

			// Adapter to ensure consistent decision format
			const userAdapter = {
				decide: (featureFlagId: string) => {
					const decision = user.decide(featureFlagId);
					return {
						...decision,
						variationKey: decision.variationKey || "",
					};
				},
			};

			// Handle multiple feature flags if configured
			const multipleFeatureFlags = getMultipleFeatureFlags(
				pageState.page.optimizelyFeatureFlags,
				userAdapter,
			);

			if (multipleFeatureFlags) {
				return Option.some({
					type: "optimizely",
					value: multipleFeatureFlags,
				});
			}

			return Option.none();
		},
	});

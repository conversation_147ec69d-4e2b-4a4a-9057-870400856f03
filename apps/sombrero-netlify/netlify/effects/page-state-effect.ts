import { Effect } from "effect";

/**
 * Effect that fetches page state data from Bloomreach API for preflight
 * requests
 *
 * This effect:
 *
 * 1. Validates that both endpoint and resolvedUrl are provided
 * 2. Filters out endpoint segments from the URL path to create a clean path
 * 3. Makes an authenticated request to the Bloomreach API with preflight=true
 * 4. Parses the JSON response with appropriate schema based on response structure
 * 5. Returns the validated page state data with proper structure
 *
 * @param params - The parameters for the effect
 * @param params.endpoint - The base API endpoint URL
 * @param params.resolvedUrl - The resolved URL path from the request
 * @returns Effect that resolves to the validated page state data or fails if
 *   endpoint/path missing or validation fails
 */
export const pageStateEffect = (preflightUrl: string) =>
	Effect.tryPromise({
		catch: (error) => `Page State Effect Error: ${error as Error}`,
		try: async () => {
			if (!preflightUrl) {
				return Effect.fail("Endpoint or path is not defined");
			}

			const response = await fetch(preflightUrl, {
				headers: {
					"x-api-key": process.env.BLOOMREACH_API_KEY ?? "",
				},
				method: "GET",
			});

			const data = await response.json();
			// TODO: Remove this when newData is removed
			if ("newData" in data) {
				// Use LegacyPageStateSchema for legacy format
				return data.newData;
			}

			// Use PageStateSchema for new format
			return data;
		},
	});

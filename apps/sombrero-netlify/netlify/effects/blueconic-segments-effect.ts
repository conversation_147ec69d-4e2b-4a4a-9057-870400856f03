import { Effect, Option, pipe } from "effect";

import type { BlueconicProfile } from "../schemas/blue-conic-profile-schema";

export type SegmentResultValue = {
	type: "segment";
	value: string;
};

/**
 * Finds a matching segment from the provided BlueConic segments array by
 * comparing against the segments in the user's profile
 */
const matchSegment = (
	blueconicSegments: Array<string>,
	profile: BlueconicProfile,
) => {
	return blueconicSegments.find((segment) => {
		return profile?.segments?.some((item) => item.id === segment);
	});
};

type ContentPersonalizationEffect = {
	profile: BlueconicProfile;
	segments: Array<string>;
};

/**
 * Effect that handles BlueConic segment matching and error handling
 *
 * The effect chain:
 *
 * 1. Early returns Option.none() if profile has no segments
 * 2. Attempts to match segments using Effect.tryPromise
 * 3. Returns Option.some() with matched segment or Option.none() if no match
 * 4. Handles and logs any errors, defaulting to Option.none()
 *
 * @returns Effect<never, never, Option<SegmentResultValue>> - An effect that
 *   resolves to an optional segment result
 */
export const blueconicSegmentsEffect = ({
	profile,
	segments,
}: ContentPersonalizationEffect) => {
	// Early return if profile has no segments - prevents unnecessary processing
	if (!profile?.segments?.length) {
		return Effect.succeed(Option.none());
	}

	return pipe(
		// Wrap the segment matching in Effect.tryPromise to handle async operations safely
		Effect.tryPromise({
			catch: (error) => {
				console.error(`Personalization Effect Error: ${String(error)}`);
				return Option.none();
			},
			try: async () => {
				const segment = matchSegment(segments, profile);

				// Return none if no matching segment is found
				if (!segment) {
					return Option.none();
				}

				// Return the matched segment wrapped in Some
				return Option.some({ type: "segment", value: segment });
			},
		}),
		// Global error handler that logs errors and ensures we always return an Option
		Effect.catchAll((error) => {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			console.error(`Blueconic Segments Effect Error: ${errorMessage}`);
			return Effect.succeed(Option.none());
		}),
	);
};

import type { ResolvedDialog } from "../../schemas/blue-conic-dialogs-schema";
import type { Offer } from "../../schemas/offer-api-schema";
import type { PageState } from "../../schemas/page-state-schema";

// Mock PageState with different component variants
export const mockPageState: PageState = {
	components: [
		// BlueConic-only component
		{
			componentName: "Banner",
			position: 1,
			variant: "blueconic-only",
		},
		// BlueConic-first component
		{
			componentName: "Promo",
			position: 2,
			variant: "blueconic-first",
		},
		// DGP-only component
		{
			componentName: "Slider",
			position: 3,
			variant: "dgp-only",
		},
		// DGP-first component
		{
			componentName: "Card",
			position: 4,
			variant: "dgp-first",
		},
		// Component with unsupported variant
		{
			componentName: "Footer",
			position: 5,
			variant: "default",
		},
	],
	identifier: "test-page",
	page: {
		functions: {
			auth: false,
			dialogs: true,
			optimizely: false,
			proposition: true,
			segments: false,
		},
		optimizelyFeatureFlags: [],
		segments: [],
	},
};

// Mock BlueConic dialogs
export const mockBlueConicDialogs: Array<ResolvedDialog> = [
	{
		componentName: "Banner",
		position: 1,
		type: "blueconic_dialogs",
		uuid: "bc-dialog-1",
	},
	{
		componentName: "Promo",
		position: 2,
		type: "blueconic_dialogs",
		uuid: "bc-dialog-2",
	},
	{
		componentName: "Card", // For DGP-first fallback test
		position: 4,
		type: "blueconic_dialogs",
		uuid: "bc-dialog-3",
	},
	// No dialog for Footer component (default variant)
];

// Mock DGP propositions
export const mockPropositions: Array<Offer> = [
	{
		channel: "ANWB.NL",
		contentId: "dgp-content-1",
		description: "First DGP offer",
		employeeId: null,
		longDescription: "Long description for first DGP offer",
		offerId: "offer-1",
		priority: 1,
		reference: null,
		responseId: "resp-1",
		score: 100,
	},
	{
		channel: "ANWB.NL",
		contentId: "dgp-content-2",
		description: "Second DGP offer",
		employeeId: null,
		longDescription: "Long description for second DGP offer",
		offerId: "offer-2",
		priority: 2,
		reference: null,
		responseId: "resp-2",
		score: 90,
	},
];

// Mock empty components
export const mockEmptyPageState: PageState = {
	components: [],
	identifier: "empty-page",
	page: {
		functions: {
			auth: false,
			dialogs: true,
			optimizely: false,
			proposition: true,
			segments: false,
		},
		optimizelyFeatureFlags: [],
		segments: [],
	},
};

// Mock with no matching BlueConic dialogs
export const mockPageStateNoMatch: PageState = {
	components: [
		{
			componentName: "UnmatchedComponent",
			position: 1,
			variant: "blueconic-only",
		},
	],
	identifier: "no-match-page",
	page: {
		functions: {
			auth: false,
			dialogs: true,
			optimizely: false,
			proposition: true,
			segments: false,
		},
		optimizelyFeatureFlags: [],
		segments: [],
	},
};

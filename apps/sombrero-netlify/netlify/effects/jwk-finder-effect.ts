import { Effect } from "effect";

import { type Jwk, type JwkError, type Jwks } from "../schemas/jwk-schema";

/**
 * Finds a JWK by key ID (kid) from the JWKS (synchronous operation)
 *
 * This effect searches for a specific JSON Web Key within a JWKS (JSON Web Key
 * Set) based on the provided key ID. If no key ID is provided, it returns the
 * first available key as a fallback.
 *
 * @param jwks - The JWKS containing the keys to search through
 * @param kid - Optional key ID to search for. If not provided, returns the
 *   first key
 * @returns Effect that resolves to the matching JWK or fails with an
 *   appropriate error
 */
export const findJwkByKidEffect = (jwks: Jwks, kid?: string) =>
	Effect.try({
		catch: (): JwkError => {
			if (jwks.keys.length === 0) {
				return "NO_KEYS_AVAILABLE";
			}
			return "JWK_NOT_FOUND";
		},
		try: (): Jwk => {
			if (jwks.keys.length === 0) {
				throw new Error("No keys available in JWKS");
			}

			// Find the key by kid, or use the first key if no kid specified
			const targetKey = kid
				? jwks.keys.find((k) => k.kid === kid)
				: jwks.keys[0];

			if (!targetKey) {
				const errorMsg = kid
					? `No key found with kid: ${kid}`
					: "No keys available in JWKS";
				throw new Error(errorMsg);
			}

			return targetKey;
		},
	});

import { Effect, Option, pipe } from "effect";

import type { ResolvedDialog } from "../schemas/blue-conic-dialogs-schema";
import type { Component } from "../schemas/page-state-schema";
import type { SombreroData } from "../schemas/sombrero-data-schema";

import { fetchBlueConicDialog } from "../helpers/fetch-blue-conic-dialog";
import { isNextCmsHost } from "../helpers/is-next-cms-host";
import { getPersonalizationProfileId } from "../helpers/personalization";
import { transformBlueConicDialogs } from "../helpers/transform-blue-conic-dialogs";

export type DialogsValue = {
	type: "dialogs";
	value: Array<ResolvedDialog>;
};

type BlueconicDialogsEffect = {
	components: Array<Component>;
	sombreroData: SombreroData;
};

/**
 * Effect that fetches and processes BlueConic dialogs for personalization
 *
 * This effect:
 *
 * 1. Checks if the request is from a CMS host (skips processing if true)
 * 2. Extracts the personalization profile ID from cookies/headers
 * 3. Fetches BlueConic dialog data for the current URL
 * 4. Transforms the raw BlueConic data into resolved dialogs
 * 5. Returns the dialogs if any are found, otherwise returns none
 *
 * @param params - The parameters for the effect
 * @param params.components - Array of page components
 * @param params.sombreroData - Request data including cookies, headers, and URL
 *   information
 * @returns Effect that resolves to Option containing DialogsValue or none if no
 *   dialogs found
 */
export const blueconicDialogsEffect = ({
	components,
	sombreroData,
}: BlueconicDialogsEffect) =>
	pipe(
		Effect.Do,
		Effect.flatMap(() => {
			const refererHeader = sombreroData.origin;

			// Skip processing for CMS hosts to avoid unnecessary dialog fetching
			if (refererHeader && isNextCmsHost(refererHeader)) {
				return Effect.succeed(Option.none());
			}

			return pipe(
				Effect.tryPromise({
					catch: (error) => {
						console.error(`Blueconic Dialogs Effect Error: ${String(error)}`);
						return Option.none();
					},
					try: async () => {
						const baseUrl = process.env.NEXT_PUBLIC_BASE_URL ?? "";

						// Extract personalization profile ID from request data
						const profileId = getPersonalizationProfileId(sombreroData.cookies);

						const blueConicUrl = new URL(
							`${baseUrl}${sombreroData.resolvedUrl}`,
						);

						// Fetch dialog configuration from BlueConic API
						const blueConicDialogData = await fetchBlueConicDialog({
							profileId: profileId ?? "",
							url: blueConicUrl.toString(),
						});

						// Transform raw BlueConic data into usable dialog objects
						const dialogs = transformBlueConicDialogs(
							blueConicDialogData,
							components,
						);

						if (!dialogs.length) {
							return Option.none();
						}

						return Option.some({
							type: "dialogs",
							value: dialogs,
						});
					},
				}),
				// Handle any errors by logging and returning none
				Effect.catchAll((error) => {
					const errorMessage =
						error instanceof Error ? error.message : String(error);
					console.error(`Blueconic Dialogs Effect Error: ${errorMessage}`);
					return Effect.succeed(Option.none());
				}),
			);
		}),
	);

import type { z } from "zod";

import { Effect, Option } from "effect";

import type { ResolvedDialog } from "../schemas/blue-conic-dialogs-schema";
import type { OfferSchema } from "../schemas/offer-api-schema";
import type { Component } from "../schemas/page-state-schema";

/** Variant types for personalization matching */
export const PERSONALIZATION_VARIANTS = {
	DGP_FIRST: "dgp-first",
	DGP_ONLY: "dgp-only",
	DIALOGS_FIRST: "dialogs-first",
	DIALOGS_ONLY: "dialogs-only",
} as const;

/** Result value type for the personalization mapping */
export type PersonalizationMatchingValue = {
	type: "personalization-mapping";
	value: Array<PersonalizationMapping>;
};

/** Personalization mapping item */
export type PersonalizationMapping = {
	componentName: string;
	id: string;
	position: number;
	provider: "blueconic_dialogs" | "dgp";
	responseId?: string;
};

/** Parameters for the personalization matching effect */
type PersonalizationMatchingEffectParams = {
	blueconicDialogs?: Array<ResolvedDialog>;
	components: Array<Component>;
	propositions?: Array<z.infer<typeof OfferSchema>>;
};

/**
 * Maps components to their respective personalization sources (DGP or
 * BlueConic) based on component variant priorities. The result is formatted as
 * an array of objects with provider, componentName, position, and id.
 *
 * Variant priorities:
 *
 * - Blueconic-first: Try BlueConic first, fallback to DGP
 * - Blueconic-only: Only use BlueConic dialogs
 * - Dgp-first: Try DGP first, fallback to BlueConic
 * - Dgp-only: Only use DGP propositions
 *
 * @param components - Page state containing component information
 * @param blueconicDialogs - Optional array of BlueConic dialogs
 * @param propositions - Optional array of propositions from DGP/Offer API
 * @returns Effect containing Option with personalization mappings
 */
export const personalizationMatchingEffect = ({
	blueconicDialogs = [],
	components,
	propositions = [],
}: PersonalizationMatchingEffectParams) =>
	Effect.tryPromise({
		catch: (error) => {
			console.error(`Personalization Matching Effect Error: ${String(error)}`);
			return Option.none();
		},
		try: async () => {
			// Early return if no components
			if (!components.length) {
				return Option.none();
			}

			// Track which propositions have been used
			let propositionIndex = 0;
			// Map to store whether a component has been matched already
			const matchedComponents = new Map<string, boolean>();
			// Result array to hold all personalization mappings
			const personalizations: Array<PersonalizationMapping> = [];

			// First pass - Handle BlueConic First and BlueConic Only
			if (blueconicDialogs.length > 0) {
				for (const component of components) {
					const key = `${component.componentName}-${component.position}`;

					// Skip if already matched
					if (matchedComponents.get(key)) continue;

					// Only process blueconic-first and blueconic-only variants in this pass
					if (
						component.variant === PERSONALIZATION_VARIANTS.DIALOGS_FIRST ||
						component.variant === PERSONALIZATION_VARIANTS.DIALOGS_ONLY
					) {
						// Find a matching BlueConic dialog for this component
						const matchingDialog = blueconicDialogs.find(
							(dialog) =>
								dialog.componentName === component.componentName &&
								dialog.position === component.position,
						);

						if (matchingDialog) {
							// Add to results if dialog found
							personalizations.push({
								componentName: component.componentName,
								id: matchingDialog.uuid,
								position: component.position,
								provider: "blueconic_dialogs",
							});

							// Mark as matched
							matchedComponents.set(key, true);
						}
					}
				}
			}

			// Second pass - Handle DGP fallbacks for BlueConic First and handle DGP First and DGP Only
			if (propositions.length > 0) {
				for (const component of components) {
					const key = `${component.componentName}-${component.position}`;

					// Skip if already matched
					if (matchedComponents.get(key)) continue;

					// Handle DGP cases (blueconic-first fallback, dgp-first, dgp-only)
					if (
						component.variant === PERSONALIZATION_VARIANTS.DIALOGS_FIRST ||
						component.variant === PERSONALIZATION_VARIANTS.DGP_FIRST ||
						component.variant === PERSONALIZATION_VARIANTS.DGP_ONLY
					) {
						// Check if we have a proposition available
						if (propositionIndex < propositions.length) {
							const proposition = propositions[propositionIndex];

							if (proposition?.contentId) {
								// Add to results
								personalizations.push({
									componentName: component.componentName,
									id: proposition.contentId,
									position: component.position,
									provider: "dgp",
									responseId: proposition.responseId,
								});

								// Mark as matched and use next proposition
								matchedComponents.set(key, true);
								propositionIndex++;
							}
						}
					}
				}
			}

			// Third pass - Handle BlueConic fallbacks for DGP First
			if (blueconicDialogs.length > 0) {
				for (const component of components) {
					const key = `${component.componentName}-${component.position}`;

					// Skip if already matched
					if (matchedComponents.get(key)) continue;

					// Only handle dgp-first components in this pass
					if (component.variant === PERSONALIZATION_VARIANTS.DGP_FIRST) {
						// Find a matching BlueConic dialog for this component
						const matchingDialog = blueconicDialogs.find(
							(dialog) =>
								dialog.componentName === component.componentName &&
								dialog.position === component.position,
						);

						if (matchingDialog) {
							// Add to results if dialog found
							personalizations.push({
								componentName: component.componentName,
								id: matchingDialog.uuid,
								position: component.position,
								provider: "blueconic_dialogs",
							});

							// Mark as matched
							matchedComponents.set(key, true);
						}
					}
				}
			}

			// If no personalization mappings were created, return None
			if (personalizations.length === 0) {
				return Option.none();
			}

			return Option.some({
				type: "personalization-mapping",
				value: personalizations,
			});
		},
	});

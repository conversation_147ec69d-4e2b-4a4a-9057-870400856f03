import { z } from "zod";

/**
 * Schema for JWK (JSON Web Key) from JWKS endpoint
 *
 * This schema validates individual JSON Web Keys used for JWT signature
 * verification. It supports both RSA and EC (Elliptic Curve) key types with
 * their respective parameters.
 *
 * @see https://tools.ietf.org/html/rfc7517 - JSON Web Key (JWK) specification
 * @see https://tools.ietf.org/html/rfc7518 - JSON Web Algorithms (JWA) specification
 */
export const JwkSchema = z.object({
	/** Algorithm intended for use with the key */
	alg: z.string().optional(),

	/** Curve name for Elliptic Curve keys (e.g., "P-256", "P-384", "P-521") */
	crv: z.string().optional(),

	/** Exponent for RSA public keys (typically "AQAB" for 65537) */
	e: z.string().optional(),

	/** Key operations that the key is intended to be used for */
	key_ops: z.array(z.string()).optional(),

	/** Key ID - used to match keys with JWT headers */
	kid: z.string().optional(),

	/** Key type - "RSA" for RSA keys, "EC" for Elliptic Curve keys */
	kty: z.string(),

	/** Modulus for RSA public keys (base64url-encoded) */
	n: z.string().optional(),

	/** Public key use - "sig" for signature verification, "enc" for encryption */
	use: z.string().optional(),

	/** X coordinate for Elliptic Curve public keys (base64url-encoded) */
	x: z.string().optional(),

	/** X.509 certificate chain (array of base64-encoded DER certificates) */
	x5c: z.array(z.string()).optional(),

	/** X.509 certificate SHA-1 thumbprint (base64url-encoded) */
	x5t: z.string().optional(),

	/** X.509 certificate SHA-256 thumbprint (base64url-encoded) */
	"x5t#S256": z.string().optional(),

	/** X.509 URL pointing to a resource for the X.509 public key certificate */
	x5u: z.string().optional(),

	/** Y coordinate for Elliptic Curve public keys (base64url-encoded) */
	y: z.string().optional(),
});

export type Jwk = z.infer<typeof JwkSchema>;

/**
 * Schema for JWKS (JSON Web Key Set) response
 *
 * This schema validates the response from the JWKS endpoint, which contains an
 * array of JSON Web Keys used for JWT signature verification.
 *
 * @see https://tools.ietf.org/html/rfc7517#section-5 - JWK Set Format
 */
export const JwksSchema = z.object({
	/** Array of JSON Web Keys */
	keys: z.array(JwkSchema),
});

export type Jwks = z.infer<typeof JwksSchema>;

/** Error types for JWK operations */
export type JwkError =
	| "JWK_NOT_FOUND"
	| "NO_KEYS_AVAILABLE"
	| "JWKS_FETCH_FAILED";

import { z } from "zod";

// Component schema used in blueconicDialogs and components
const ComponentSchema = z.object({
	componentName: z.string(),
	position: z.number(),
	variant: z.string(),
});

// Feature flag schema used in optimizelyFeatureFlags
const FeatureFlagSchema = z.object({
	featureFlagId: z.string(),
	variants: z.array(z.string()).optional(),
});

// Page functions schema used in page.functions
const PageFunctionsSchema = z.object({
	auth: z.boolean(),
	dialogs: z.boolean(),
	optimizely: z.boolean(),
	proposition: z.boolean(),
	segments: z.boolean(),
});

// Page schema used in page
const PageSchema = z.object({
	functions: PageFunctionsSchema,
	optimizelyFeatureFlags: z.array(
		z.object({
			featureFlagId: z.string(),
		}),
	),
	segments: z.array(z.string()),
});

// Main page state schema
export const PageStateSchema = z.object({
	components: z.array(ComponentSchema),
	identifier: z.string(),
	page: PageSchema,
});

export const LegacyPageStateSchema = z.object({
	authenticated: z.boolean(),
	blueconicDialogs: z.array(
		z.object({
			componentName: z.string(),
			position: z.union([z.number(), z.array(z.number())]),
			variant: z.string(),
		}),
	),
	blueconicSegments: z.array(z.string()),
	identifier: z.string(),
	newData: z.object({
		components: z.array(ComponentSchema),
		identifier: z.string(),
		page: PageSchema,
	}),
	optimizelyFeatureFlagId: z.string(),
	optimizelyFeatureFlags: z.array(FeatureFlagSchema),
	optimizelyVariants: z.array(z.string()),
	relevanceEnabled: z.boolean(),
});

// Export types derived from the schemas
export type Component = z.infer<typeof ComponentSchema>;
export type FeatureFlag = z.infer<typeof FeatureFlagSchema>;
export type PageFunctions = z.infer<typeof PageFunctionsSchema>;
export type Page = z.infer<typeof PageSchema>;
export type PageState = z.infer<typeof PageStateSchema>;

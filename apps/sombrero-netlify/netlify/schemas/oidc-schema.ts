import { z } from "zod";

/**
 * Schema for OIDC configuration response
 *
 * This schema validates the OpenID Connect configuration response from the
 * /.well-known/openid-configuration endpoint. It includes all standard OIDC
 * endpoints and configuration parameters.
 */
export const OidcConfigSchema = z.object({
	/** Authorization endpoint URL - where users are redirected to authenticate */
	authorization_endpoint: z.string().url().optional(),

	/** End session endpoint URL - where users are redirected to logout */
	end_session_endpoint: z.string().url().optional(),

	/**
	 * Issuer URL - identifies the principal that issued the JWT This must match
	 * the 'iss' claim in JWT tokens
	 */
	issuer: z.string().url(),

	/** JSON Web Key Set URI - endpoint containing public keys for JWT verification */
	jwks_uri: z.string().url(),

	/** Token endpoint URL - where authorization codes are exchanged for tokens */
	token_endpoint: z.string().url().optional(),

	/** UserInfo endpoint URL - where user information can be retrieved */
	userinfo_endpoint: z.string().url().optional(),
});

export type OidcConfig = z.infer<typeof OidcConfigSchema>;

/** Error types for OIDC configuration operations */
export type OidcConfigError =
	| "INVALID_CIAM_LOGIN_URL_FORMAT"
	| "MISSING_CIAM_LOGIN_URL"
	| "OIDC_CONFIG_FETCH_FAILED";

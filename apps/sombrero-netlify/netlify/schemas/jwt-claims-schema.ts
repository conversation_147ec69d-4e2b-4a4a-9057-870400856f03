import { z } from "zod";

/**
 * Schema for validating JWT claims from AccessToken cookies
 *
 * This schema validates the essential claims required for authentication:
 *
 * - Iss: Issuer claim - must match expected CIAM issuer URL
 * - Exp: Expiration time claim - used to check if token is expired
 * - Iat: Issued at claim - when the token was issued
 * - Sub: Subject claim - identifies the user (optional)
 * - Aud: Audience claim - identifies the intended recipient (optional)
 */
export const JwtClaimsSchema = z.object({
	/**
	 * Audience claim - identifies the recipients that the JWT is intended for
	 * Each principal intended to process the JWT MUST identify itself with a
	 * value in the audience claim
	 */
	aud: z.union([z.string(), z.array(z.string())]).optional(),

	/**
	 * Expiration time claim - identifies the expiration time on or after which
	 * the JWT MUST NOT be accepted for processing (Unix timestamp)
	 */
	exp: z.number().int().positive("Expiration time must be a positive integer"),

	/**
	 * Issued at claim - identifies the time at which the JWT was issued (Unix
	 * timestamp)
	 */
	iat: z.number().int().positive("Issued at time must be a positive integer"),

	/**
	 * Issuer claim - identifies the principal that issued the JWT Must match the
	 * expected issuer URL for the current environment
	 */
	iss: z.string().min(1, "Issuer claim is required"),

	/** JWT ID claim - provides a unique identifier for the JWT */
	jti: z.string().optional(),

	/**
	 * Not before claim - identifies the time before which the JWT MUST NOT be
	 * accepted
	 */
	nbf: z.number().int().optional(),

	/**
	 * Subject claim - identifies the principal that is the subject of the JWT
	 * Usually contains the user identifier
	 */
	sub: z.string().optional(),
});

export type JwtClaims = z.infer<typeof JwtClaimsSchema>;

/** Validation result for JWT claims */
export type JwtValidationResult =
	| { claims: JwtClaims; success: true }
	| { error: JwtValidationError; success: false };

/** Possible JWT validation errors */
export type JwtValidationError =
	| "JWT_CLAIMS_INVALID"
	| "JWT_EXPIRED"
	| "JWT_ISSUER_MISMATCH"
	| "JWT_NOT_YET_VALID"
	| "JWT_PARSE_ERROR"
	| "JWT_SIGNATURE_INVALID";

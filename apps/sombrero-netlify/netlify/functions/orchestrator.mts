import type { Context } from "@netlify/functions";
import type { z } from "zod";

import { Effect, Option } from "effect";

import type { ResolvedDialog } from "../schemas/blue-conic-dialogs-schema";
import type { CustomerId } from "../schemas/customer-id-schema";
import type { OfferSchema } from "../schemas/offer-api-schema";

import {
	blueconicDialogsEffect,
	type DialogsValue,
} from "../effects/blueconic-dialogs-effect";
import { blueconicProfileEffect } from "../effects/blueconic-profile-effect";
import {
	blueconicSegmentsEffect,
	type SegmentResultValue,
} from "../effects/blueconic-segments-effect";
import { getCustomerIdEffect } from "../effects/customer-id-effect";
import {
	offerApiEffect,
	type PropositionsValue,
} from "../effects/offer-api-effect";
import { optimizelyEffect } from "../effects/optimizely-effect";
import { pageStateEffect } from "../effects/page-state-effect";
import { personalizationMatchingEffect } from "../effects/personalization-matching-effect";
import { previewRedirectEffect } from "../effects/preview-redirect-effect";
import { requireCookieConsentEffect } from "../effects/requires-cookie-consent-effect";
import { userAuthEffect } from "../effects/user-auth-effect";
import {
	type BlueconicProfile,
	BlueconicProfileSchema,
} from "../schemas/blue-conic-profile-schema";
import { PageStateSchema } from "../schemas/page-state-schema";
import { SombreroDataSchema } from "../schemas/sombrero-data-schema";

type EffectResultType = "dialogs" | "propositions" | "segment";
type EffectResult = {
	result: Option.Option<DialogsValue | PropositionsValue | SegmentResultValue>;
	type: EffectResultType;
};

/**
 * Type guard to verify if a value matches the expected TypedResult structure.
 *
 * This function ensures that the value has the correct shape before we attempt
 * to access its properties safely.
 *
 * @param value - The value to check
 * @returns True if the value matches the expected structure
 */
const isTypedResult = (
	value: unknown,
): value is DialogsValue | PropositionsValue | SegmentResultValue => {
	return (
		value !== null &&
		typeof value === "object" &&
		"type" in value &&
		"value" in value
	);
};

/**
 * Main orchestrator function for the Sombrero personalization system.
 *
 * This function serves as the central coordinator for all effects, including
 * user authentication, A/B testing, offer management, BlueConic dialogs, and
 * segment matching. It processes incoming requests and returns personalized
 * content recommendations.
 *
 * The function handles the complete personalization flow:
 *
 * 1. Validates request method and parses input data
 * 2. Determines page state and handles preview redirects
 * 3. Manages user authentication when required
 * 4. Executes A/B testing through Optimizely
 * 5. Verifies cookie consent before personalized processing
 * 6. Runs personalization effects in parallel for performance
 * 7. Matches components to appropriate content sources
 * 8. Returns comprehensive personalization data
 *
 * @example
 * 	```typescript
 * 	// POST request with Sombrero data
 * 	const response = await orchestrator(request, context);
 * 	// Returns: { hasAuthState: boolean, personalization: {...}, customerId?: {...} }
 * 	```;
 *
 * @param request - The incoming HTTP request containing Sombrero data
 * @param context - Netlify function context for additional request information
 * @returns Promise resolving to an HTTP response with personalization data
 */
export default async (
	request: Request,
	context: Context,
): Promise<Response> => {
	// Only accept POST requests for orchestrator operations
	if (request.method !== "POST") {
		return new Response(
			JSON.stringify({
				message: "Method not allowed",
			}),
			{
				status: 405,
			},
		);
	}

	// Parse and store the request body once to avoid multiple reads
	const requestBody = await request.json();

	// Validate and parse incoming Sombrero data using Zod schema
	const sombreroData = SombreroDataSchema.parse(requestBody);

	// Determine the current page state based on endpoint and resolved URL
	// This includes page configuration and component definitions
	const pageState = await pageStateEffect(sombreroData.preflightUrl).pipe(
		Effect.andThen((data) => PageStateSchema.parse(data)),
		Effect.runPromise,
	);

	// Determine if personalization is enabled based on page state
	const hasPersonalization =
		pageState.page.functions.dialogs || pageState.page.functions.proposition;

	// Check if this is a preview request that needs redirection
	const previewRedirect = await previewRedirectEffect({
		sombreroData,
	}).pipe(Effect.runPromise);

	// Handle preview redirects immediately
	if (Option.isSome(previewRedirect)) {
		return new Response(
			JSON.stringify({
				hasAuthState: true,
				redirect: previewRedirect.value.redirectUrl,
			}),
			{
				status: 302,
			},
		);
	}

	// Handle user authentication if required by the current page
	// Only runs when page.functions.auth is true
	const authState = await userAuthEffect({
		sombreroData,
	})
		.pipe(Effect.andThen((data) => data))
		.pipe(Effect.when(() => pageState.page.functions.auth))
		.pipe(Effect.runPromise);

	const hasAuthState = Option.isSome(authState);

	// Redirect to authentication if the user is not properly authenticated
	if (Option.isSome(authState) && !authState.value.isValid) {
		return new Response(
			JSON.stringify({
				hasAuthState,
				redirect: authState.value.redirectUrl,
			}),
			{
				status: 302,
			},
		);
	}

	// Execute Optimizely A/B testing experiments
	const optimizelyResult = await optimizelyEffect({
		context,
		pageState,
		request,
		sombreroData,
	}).pipe(Effect.runPromise);

	// Initialize personalization data object with Optimizely results
	const personalization: Record<string, unknown> = {};
	if (Option.isSome(optimizelyResult)) {
		const resultValue = optimizelyResult.value;
		const typedResult = resultValue as { type: string; value: string };
		if (typedResult.type === "optimizely") {
			personalization.optimizelyVariations = typedResult.value;
		}
	}

	// Verify that the user has given appropriate cookie consent
	// Required before processing any personalized content
	const cookieConsent = await requireCookieConsentEffect({
		sombreroData,
		validConsentLevel: "LIMITED",
	}).pipe(Effect.runPromise);

	// If no cookie consent, return early with only A/B testing results
	if (Option.isNone(cookieConsent)) {
		return new Response(
			JSON.stringify({
				hasAuthState,
				hasPersonalization,
				message: "No cookie consent given.",
				personalization,
			}),
			{
				status: 200,
			},
		);
	}

	// Initialize variables for parallel effect processing
	const effectPromises: Array<Promise<EffectResult>> = [];

	let customerId = Option.none<CustomerId>();
	let propositions: Array<z.infer<typeof OfferSchema>> = [];
	let blueconicDialogs: Array<ResolvedDialog> = [];
	let blueconicProfile: BlueconicProfile | undefined;
	// Fetch BlueConic profile data if needed for propositions or segments
	// This is a prerequisite for both offer API calls and segment matching
	if (
		pageState.page.functions.proposition ||
		pageState.page.functions.segments
	) {
		const blueconicProfileResult = await blueconicProfileEffect({
			sombreroData,
		}).pipe(Effect.runPromise);

		// Safely parse and validate the BlueConic profile data
		blueconicProfile =
			Option.flatMap(blueconicProfileResult, (result) => {
				try {
					return Option.some(BlueconicProfileSchema.parse(result));
				} catch {
					return Option.none();
				}
			}).pipe(Option.getOrNull) ?? undefined;
	}

	// Process offer propositions if enabled for the current page
	if (pageState.page.functions.proposition) {
		// Extract customer ID from BlueConic profile and Sombrero data
		customerId = await getCustomerIdEffect({
			blueconicProfile: Option.fromNullable(blueconicProfile),
			sombreroData,
		}).pipe(Effect.runPromise);

		// Only proceed with offer API if we have a valid customer ID
		if (Option.isSome(customerId) && customerId.value.relationNumber !== "") {
			// Find the first DGP (Dynamic Generation Platform) compatible component
			// This determines the position parameter for the offer API call
			const firstDgpComponent = pageState.components.find(
				(component) =>
					component.variant === "dgp-first" || component.variant === "dgp-only", // blueconic-first can fallback to DGP
			);

			// Only make offer API call if we have a valid component position
			if (firstDgpComponent?.componentName) {
				effectPromises.push(
					offerApiEffect({
						channel: "ANWB.NL", // Fixed channel identifier
						customerId: customerId.value.relationNumber,
						origin: sombreroData.origin,
						position: firstDgpComponent.componentName, // Component name serves as position identifier
						subchannel: sombreroData.resolvedUrl,
					})
						.pipe(Effect.runPromise)
						.then((result) => {
							// Store propositions for later use in personalization matching
							if (
								Option.isSome(result) &&
								result.value.type === "propositions"
							) {
								propositions = result.value.value;
							}
							return { result, type: "propositions" } as EffectResult;
						})
						.catch((error: unknown) => {
							console.error("Error processing offers:", error);
							return {
								result: Option.none(),
								type: "propositions",
							} as EffectResult;
						}),
				);
			}
		}
	}

	// Process BlueConic dialogs if enabled for the current page
	if (pageState.page.functions.dialogs) {
		effectPromises.push(
			blueconicDialogsEffect({
				components: pageState.components,
				sombreroData,
			})
				.pipe(Effect.runPromise)
				.then((result) => {
					// Store BlueConic dialogs for later component matching
					if (Option.isSome(result) && result.value.type === "dialogs") {
						blueconicDialogs = result.value.value;
					}
					return { result, type: "dialogs" } as EffectResult;
				})
				.catch((error: unknown) => {
					console.error("Error processing dialogs:", error);
					return { result: Option.none(), type: "dialogs" } as EffectResult;
				}),
		);
	}

	// Process BlueConic segment matching if enabled for the current page
	if (pageState.page.functions.segments) {
		effectPromises.push(
			blueconicSegmentsEffect({
				profile: blueconicProfile,
				segments: pageState.page.segments,
			})
				.pipe(Effect.runPromise)
				.then((result) => ({ result, type: "segment" }) as EffectResult)
				.catch((error: unknown) => {
					console.error("Error processing segments:", error);
					return { result: Option.none(), type: "segment" } as EffectResult;
				}),
		);
	}

	// Execute all personalization effects in parallel for optimal performance
	const results = await Promise.all(effectPromises);

	// Process and extract segment results from the parallel execution
	for (const { result } of results) {
		Option.match(result, {
			onNone: () => void 0, // No-op for failed effects
			onSome: (value) => {
				// Extract segment data and add to personalization object
				if (isTypedResult(value) && value.type === "segment") {
					personalization.blueconicSegment = value.value;
				}
			},
		});
	}

	// Execute personalization matching to map components to appropriate content sources
	// This combines propositions and dialogs with page components
	const personalizedComponents = await personalizationMatchingEffect({
		blueconicDialogs,
		components: pageState.components,
		propositions,
	}).pipe(Effect.runPromise);

	// Add personalization mappings to the response if successfully generated
	if (
		Option.isSome(personalizedComponents) &&
		personalizedComponents.value.type === "personalization-mapping" &&
		Array.isArray(personalizedComponents.value.value)
	) {
		// Store the array of component-to-content mappings
		personalization.personalizedComponents = personalizedComponents.value.value;
	}

	// Return the comprehensive personalization response
	return new Response(
		JSON.stringify({
			customerId: Option.isSome(customerId) ? customerId.value : undefined,
			hasAuthState,
			hasPersonalization,
			personalization,
		}),
		{
			status: 200,
		},
	);
};

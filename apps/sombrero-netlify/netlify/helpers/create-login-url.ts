import type { JwtValidationError } from "../schemas/jwt-claims-schema";

/**
 * Creates appropriate login URL based on JWT validation error
 *
 * @param baseLoginUrl - The base login URL
 * @param error - The JWT validation error
 * @returns Complete login URL with appropriate parameters
 */
export const createLoginUrl = (
	baseLoginUrl: string,
	error: JwtValidationError,
): string => {
	const url = new URL(baseLoginUrl);

	// Skip the ?prompt=login when error is JWT_EXPIRED so that a possible existing session can refresh the token
	if (error !== "JWT_EXPIRED") {
		url.searchParams.set("prompt", "login");
	}

	return url.toString();
};

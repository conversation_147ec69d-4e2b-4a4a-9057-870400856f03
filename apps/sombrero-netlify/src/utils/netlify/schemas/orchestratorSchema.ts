import { boolean, z } from "zod";

// Define specific schemas for each type of personalization
const CustomerIdSchema = z.object({
	relationNumber: z.string(),
	reputation: z.number(),
	source: z.literal("crs").or(z.literal("blueconic")),
});

// Define schema for personalized component mapping
const PersonalizationMappingSchema = z.object({
	componentName: z.string(),
	id: z.string(),
	position: z.number(),
	provider: z.union([z.literal("dgp"), z.literal("blueconic_dialogs")]),
	responseId: z.string().optional(),
});

const PersonalizationSchema = z.object({
	// Blueconic segment
	blueconicSegment: z.string().optional(),

	// Optimizely variations
	optimizelyVariations: z.string().optional(),

	// Personalized components (array of personalization mappings)
	personalizedComponents: z.array(PersonalizationMappingSchema).optional(),
});

export const OrchestratorResponseSchema = z.object({
	customerId: CustomerIdSchema.optional(),
	hasAuthState: boolean().optional(),
	hasPersonalization: boolean().optional(),
	message: z.string().optional(),
	personalization: z
		.union([PersonalizationSchema, z.object({})])
		.optional()
		.default({}),
	redirect: z.string().optional(),
	statusCode: z.number().optional(),
});

export type OrchestratorResponse = z.infer<typeof OrchestratorResponseSchema>;

import type { GetServerSidePropsContext } from "next";

import { ZodError } from "zod";

import { getBaseConfig } from "../bloomreach";
import { getPreflightUrl } from "../bloomreach/getPreflightUrl";
import { createOrigin } from "./helpers/createOrigin";
import {
	type OrchestratorResponse,
	OrchestratorResponseSchema,
} from "./schemas/orchestratorSchema";

export const fetchOrchestrator = async (
	ctx: GetServerSidePropsContext,
): Promise<OrchestratorResponse> => {
	// Don't fetch from orchestrator when not running on Netlify (local dev or deployed)
	if (!ctx.req.headers["x-nf-request-id"]) {
		return {
			hasAuthState: false,
			hasPersonalization: false,
			personalization: {},
			statusCode: 200,
		};
	}

	const { endpoint } = getBaseConfig(ctx.resolvedUrl);
	const origin = createOrigin(ctx);

	const body = JSON.stringify({
		cookies: ctx.req.cookies,
		endpoint,
		headers: ctx.req.headers,
		origin,
		preflightUrl: getPreflightUrl(ctx.resolvedUrl),
		refererHeader: ctx.req.headers.referer,
		resolvedUrl: ctx.resolvedUrl,
	});

	try {
		const response = await fetch(`${origin}/.netlify/functions/orchestrator`, {
			body,
			headers: {
				"Content-Type": "application/json",
				"X-Bloomreach-Netlify-Token":
					process.env.NETLIFY_BLOOMREACH_API_KEY ?? "",
			},
			method: "POST",
		});

		const data = await response.json();
		const validatedResponse = OrchestratorResponseSchema.parse(data);
		return validatedResponse;
	} catch (error) {
		// Check if this is a Zod validation error
		if (error instanceof ZodError) {
			console.warn("Orchestrator response validation failed:", error);
			return {
				hasAuthState: false,
				hasPersonalization: false,
				message: "Validation error occurred",
				personalization: {},
				statusCode: 422,
			};
		}

		console.warn("Error fetching from orchestrator:", error);
		return {
			hasAuthState: false,
			hasPersonalization: false,
			message: "Orchestrator fetching error occurred",
			personalization: {},
			statusCode: 500,
		};
	}
};

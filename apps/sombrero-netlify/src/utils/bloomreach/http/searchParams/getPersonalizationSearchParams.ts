import type { OrchestratorResponse } from "../../../netlify/schemas/orchestratorSchema";

/**
 * Creates URLSearchParams for personalized components from the orchestrator
 * response.
 *
 * Format for each parameter:
 * `personalization_components=<provider>;<componentName>;<position>;<id>`
 *
 * Where:
 *
 * - Provider: Either 'dgp' or 'blueconic_dialogs'
 * - ComponentName: Name of the component
 * - Position: Position of the component
 * - Id: Either a proposition ID or BlueConic UUID
 *
 * @param personalization - Personalization object from the orchestrator
 *   response
 * @param hasPersonalization - Flag indicating if personalization is enabled in
 *   the page state
 * @returns URLSearchParams containing personalization parameters
 */
export const getPersonalizationSearchParams = (
	personalization: OrchestratorResponse["personalization"],
	hasPersonalization?: boolean,
) => {
	const searchParams = new URLSearchParams();

	// Add personalization_active param if personalization is enabled in page state
	if (hasPersonalization) {
		searchParams.append("personalization_active", "true");
	}

	// Add personalized components if they exist
	const personalizedComponents =
		"personalizedComponents" in personalization
			? personalization.personalizedComponents
			: undefined;

	if (
		hasPersonalization &&
		personalizedComponents &&
		personalizedComponents.length > 0
	) {
		// Add each personalized component as a separate personalization_components parameter
		personalizedComponents.forEach((component) => {
			const paramValue = `${component.componentName};${component.position};${component.id}`;
			searchParams.append("personalization_components", paramValue);
		});
	}

	return searchParams;
};

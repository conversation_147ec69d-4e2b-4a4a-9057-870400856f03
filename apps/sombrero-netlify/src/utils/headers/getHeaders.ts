import Headers, { CacheControl } from "@mjackson/headers";

import {
	WITH_NETLIFY_CACHE,
	WITH_PERSONALIZATION_CACHE,
	WITHOUT_CACHE,
} from "./constants";
import { NetlifyCacheControl } from "./NetlifyCacheControl";

type Options = {
	applyCacheHeaders?: boolean;
	bloomreachCacheIdentifier?: string;
	personalizedPage: boolean;
};

export const getHeaders = ({
	applyCacheHeaders = false,
	bloomreachCacheIdentifier,
	personalizedPage,
}: Options) => {
	const headers = new Headers({
		"Cache-Control": new CacheControl(WITHOUT_CACHE),
		"Referrer-Policy": "strict-origin-when-cross-origin",
	});

	if (!applyCacheHeaders) {
		return headers;
	}

	if (!personalizedPage) {
		headers.set(
			"Netlify-CDN-Cache-Control",
			new NetlifyCacheControl({
				...WITH_NETLIFY_CACHE,
				durable: true,
			}).toString(),
		);

		headers.set("Netlify-Cache-Tag", bloomreachCacheIdentifier ?? "");

		return headers;
	}

	headers.set(
		"Netlify-CDN-Cache-Control",
		new NetlifyCacheControl({
			...WITH_PERSONALIZATION_CACHE,
		}).toString(),
	);

	return headers;
};

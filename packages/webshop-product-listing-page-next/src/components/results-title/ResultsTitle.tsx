import { LabelTitle } from "@anwb/poncho/components/typography";

import { useFiltersState, useProductsState } from "../../context";

function ResultsTitle() {
	const { pending, productCount } = useProductsState();
	const { searchTerm } = useFiltersState();

	const getResultsTitleText = (count: number): string => {
		let resultsTitle: string;
		switch (count) {
			case 0:
				resultsTitle = "Geen resultaten";
				break;
			case 1:
				resultsTitle = "1 resultaat";
				break;
			default:
				resultsTitle = `${count} resultaten`;
				break;
		}
		return searchTerm ? `${resultsTitle} voor ${searchTerm}` : resultsTitle;
	};

	return (
		<LabelTitle as="h2">
			{!pending && getResultsTitleText(productCount)}
		</LabelTitle>
	);
}

export default ResultsTitle;

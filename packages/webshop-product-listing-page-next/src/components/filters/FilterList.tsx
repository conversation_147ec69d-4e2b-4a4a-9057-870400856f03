import { ComponentTitle } from "@anwb/poncho/components/typography";
import { usePrismicState } from "@anwb/webshop-prismic-slice";
import { useEffect } from "react";

import type {
	CheckboxFilter,
	ColorFilter,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON>ilter,
} from "../../data";

import { useFiltersDispatch, useFiltersState } from "../../context";
import CategoryTree from "../category-tree/CategoryTree";
import CollapsablePanel from "../collapsable-panel/CollapsablePanel";
import FilterCheckbox from "./FilterCheckbox";
import FilterColor from "./FilterColor";
import FilterSize from "./FilterSize";
import FilterSlider from "./FilterSlider";
import {
	FilterListCategoriesContainer,
	FilterListContainer,
	FiltersPane,
} from "./styles/filter-list.styled";

type Props = {
	variant: "desktop" | "mobile";
};
function FilterList({ variant }: Props) {
	const { filters, searchTerm } = useFiltersState();
	const filtersDispatch = useFiltersDispatch();
	const prismicState = usePrismicState();
	useEffect(() => {
		filtersDispatch({
			type: "SET_KTYPE_VALUE",
			value: prismicState.auto_filter.kType ?? null,
		});
	}, [prismicState?.auto_filter?.kType, filtersDispatch]);

	const listReference = `product-listing-page-filter-list-${variant}`;

	const hiddenFilters = [
		"anwb_bandenmaat_sneeuwkettingen",
		"anwb_sneeuwketting_dikte_in_mm",
	];

	return (
		<FiltersPane variant={variant}>
			<ComponentTitle as="h2">Filters</ComponentTitle>
			<FilterListContainer role="tablist">
				{!searchTerm && (
					<FilterListCategoriesContainer>
						<CollapsablePanel
							id="category"
							initialOpen={variant !== "mobile"}
							label="Categorieën"
							reference={listReference}
						>
							<CategoryTree />
						</CollapsablePanel>
					</FilterListCategoriesContainer>
				)}
				{filters
					.filter(({ key }) => !hiddenFilters.includes(key))
					.map((filter) => {
						const filterReference = `${variant}-${filter.title}`;
						return (
							<CollapsablePanel
								id={filter.key}
								initialOpen={variant !== "mobile"}
								key={filter.key}
								label={filter.title}
								reference={listReference}
							>
								{filter.variant === "slider" && (
									<FilterSlider filter={filter as SliderFilter} />
								)}
								{filter.variant === "checkbox" && (
									<FilterCheckbox
										filter={filter as CheckboxFilter}
										reference={filterReference}
									/>
								)}
								{filter.variant === "color" && (
									<FilterColor
										filter={filter as ColorFilter}
										reference={filterReference}
									/>
								)}
								{filter.variant === "size" && (
									<FilterSize
										filter={filter as SizeFilter}
										reference={filterReference}
									/>
								)}
							</CollapsablePanel>
						);
					})}
			</FilterListContainer>
		</FiltersPane>
	);
}

export default FilterList;

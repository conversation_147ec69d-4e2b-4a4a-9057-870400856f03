import { BrandRefreshCard } from "@anwb/poncho/components/card";
import ReviewSummary from "@anwb/poncho/components/review-summary";
import { Tag } from "@anwb/poncho/components/tags";
import { formatPrice } from "@anwb/webshop-helpers";
import { forwardRef, useCallback, useState } from "react";

import type {
	ProductImage,
	ProductLabel,
	ProductPrice,
	Seller,
} from "../../data/getFilteredAndSortedProducts";

import ImageWrapper from "./ImageWrapper";
import {
	CardImageError,
	ColorOverwrite,
	FavoriteButton,
	MultiColorSupportText,
	VariantsWrapper,
} from "./styles/product-tile.styled";
import VariantList from "./VariantList";

type ProductProps = {
	brand: null | string;
	colorCount: number;
	displayName: string;
	id: string;
	image: ProductImage;
	isFavorite: boolean;
	labels: Array<ProductLabel>;
	link: { href: string; onClick: React.MouseEventHandler<HTMLAnchorElement> };
	price: ProductPrice;
	reviewCount: number;
	reviewRating: number;
	seller: null | Seller;
	sku: string;
	toggleFavorite: () => void;
	variants: [] | Array<Variant>;
};

export type Variant = {
	image: {
		alt: string;
		sizes: string;
		srcSet: string;
		url: string;
	};
	sku: string;
	targetUrl: string;
};

const ProductTile = forwardRef<HTMLDivElement, ProductProps>(
	(
		{
			brand,
			colorCount,
			displayName,
			id,
			image,
			isFavorite,
			labels,
			link,
			price,
			reviewCount,
			reviewRating,
			seller,
			sku,
			toggleFavorite,
			variants,
		}: ProductProps,
		ref,
	) => {
		const hasSalePrice = !!price.sale;
		const [selectedVariant, setSelectedVariant] = useState<Variant>({
			image: image,
			sku: sku,
			targetUrl: link.href,
		});

		const handleChangeVariant = useCallback(
			({ image, sku, targetUrl }: Variant) => {
				setSelectedVariant({
					image: image,
					sku: sku,
					targetUrl: targetUrl || link.href,
				});
			},
			[variants],
		);

		const productReviewRating = reviewRating * 2 || 0; //Multiply so it uses a 0 - 10 scale.
		return (
			<div ref={ref}>
				<BrandRefreshCard>
					<FavoriteButton onChange={toggleFavorite} selected={isFavorite} />
					<BrandRefreshCard.Header
						aria-label={displayName}
						href={selectedVariant.targetUrl || link.href}
						id={id}
						onClick={link.onClick}
					>
						<ImageWrapper orientation={"portrait"}>
							{selectedVariant?.image?.url ? (
								<BrandRefreshCard.Image
									alt={selectedVariant.image.alt || displayName}
									src={selectedVariant.image.url}
									srcset={selectedVariant.image.srcSet}
								/>
							) : (
								<CardImageError />
							)}
							<BrandRefreshCard.LabelGroup>
								{labels.map(({ text, variant }) => (
									<BrandRefreshCard.Label
										key={`${variant}-${id}`}
										variant={variant}
									>
										<BrandRefreshCard.LabelText>
											{text}
										</BrandRefreshCard.LabelText>
									</BrandRefreshCard.Label>
								))}
							</BrandRefreshCard.LabelGroup>
						</ImageWrapper>
						<BrandRefreshCard.ContentGroup>
							<BrandRefreshCard.SupportText>
								{brand}
							</BrandRefreshCard.SupportText>
							<BrandRefreshCard.ContentGroup>
								<BrandRefreshCard.Title title={displayName} />
							</BrandRefreshCard.ContentGroup>
						</BrandRefreshCard.ContentGroup>
					</BrandRefreshCard.Header>
					<BrandRefreshCard.Content>
						<BrandRefreshCard.ContentGroup>
							{productReviewRating > 0 && (
								// Inline style for A-B test for ratings. Div wrapper can be removed after the test
								<div data-test="review-summary" style={{ display: "none" }}>
									<BrandRefreshCard.ContentGroup inline>
										<ReviewSummary
											numberOfReviews={reviewCount}
											rating={productReviewRating}
										/>
									</BrandRefreshCard.ContentGroup>
								</div>
							)}

							{seller?.active && (
								<BrandRefreshCard.ContentGroup inline>
									<BrandRefreshCard.Price>
										<ColorOverwrite>
											{formatPrice(
												hasSalePrice
													? price.sale || undefined
													: price.regular || undefined,
												{ euroSign: false },
											)}
										</ColorOverwrite>
									</BrandRefreshCard.Price>
									{hasSalePrice && price.sale !== price.regular && (
										<BrandRefreshCard.SupportText>
											<s>
												{formatPrice(price.regular || undefined, {
													euroSign: false,
												})}
											</s>
										</BrandRefreshCard.SupportText>
									)}
								</BrandRefreshCard.ContentGroup>
							)}

							{seller?.active && seller?.name && (
								<BrandRefreshCard.ContentGroup inline>
									<BrandRefreshCard.SupportText>
										Verkoop door{" "}
										<>
											{seller.link ? (
												<a href={`/webwinkel/${seller.link}`} target="_self">
													{seller.name}
												</a>
											) : (
												seller.name
											)}
										</>
									</BrandRefreshCard.SupportText>
								</BrandRefreshCard.ContentGroup>
							)}

							{colorCount > 1 && (
								<BrandRefreshCard.ContentGroup inline>
									<VariantsWrapper>
										<MultiColorSupportText>
											<Tag>
												<Tag.Label>Meerdere kleuren beschikbaar</Tag.Label>
											</Tag>
										</MultiColorSupportText>
										<VariantList
											onChangeVariant={handleChangeVariant}
											selectedVariant={selectedVariant}
											variants={variants}
										/>
									</VariantsWrapper>
								</BrandRefreshCard.ContentGroup>
							)}
						</BrandRefreshCard.ContentGroup>
					</BrandRefreshCard.Content>
				</BrandRefreshCard>
			</div>
		);
	},
);
ProductTile.displayName = "ProductTile";

export default ProductTile;

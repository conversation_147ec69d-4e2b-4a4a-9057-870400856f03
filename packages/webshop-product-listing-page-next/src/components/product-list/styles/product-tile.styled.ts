import type { Theme } from "@anwb/poncho/design-tokens/theme";

import { BrandRefreshCard } from "@anwb/poncho/components/card";
import { ImageError } from "@anwb/poncho/components/image/components/ImageError";
import Typography, { BodyTextXs } from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const ColorOverwrite = styled.span.attrs({
	name: "color-overwrite",
})(
	({ theme }: { theme: Theme }) => css`
		color: ${theme.colors.base.textBody};
	`,
);

export const FavoriteButton = styled(BrandRefreshCard.Favorite)`
	cursor: pointer;

	/* TODO: !!TEMPORARY!! Remove display: none when favorites on PLP is tested on production */
	display: none;
`;

export const CardImageError = styled(ImageError).attrs({
	className: "",
	dataTest: "image-error",
	inline: true,
	width: "full",
})`
	margin: 0;
	width: 100%;
	height: 100%;
`;

export const MultiColorSupportText = styled(Typography).attrs({
	name: "multi-color",
	variant: "support-text",
})(
	({ theme }: { theme: Theme }) => css`
		color: ${theme.colors.informative.subTitlePrice};
	`,
);

export const VariantContainer = styled.span.attrs({
	name: "variant-container",
})(
	({ theme }: { theme: Theme }) => css`
		/* overriding poncho styling to show correct thumbnail sizes */
		& > * > div > div {
			width: ${pxToRem(32)};
			height: ${pxToRem(43)};
		}

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			& > * > div > div {
				width: ${pxToRem(40)};
				height: ${pxToRem(53)};
			}
		}
	`,
);

export const VariantListContainer = styled.div.attrs({
	name: "variant-list-container",
})(
	({ theme }: { theme: Theme }) => css`
		display: flex;
		gap: ${pxToRem(theme.spacing[100])};
	`,
);

export const VariantsWrapper = styled.div.attrs({
	name: "variants-wrapper",
})(
	({ theme }: { theme: Theme }) => css`
		${MultiColorSupportText} {
			display: none;
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.sm)}) {
			${MultiColorSupportText} {
				display: flex;
			}

			${VariantListContainer} {
				display: none;
			}
		}
	`,
);

export const VariantsLeftCounter = styled(BodyTextXs).attrs({
	name: "variant-left-counter",
})(
	({ theme }: { theme: Theme }) => css`
		color: ${theme.colors.informative.subTitlePrice};
	`,
);

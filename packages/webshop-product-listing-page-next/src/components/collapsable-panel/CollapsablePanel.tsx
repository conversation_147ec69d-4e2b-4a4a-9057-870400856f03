import type { ReactNode } from "react";

import { useState } from "react";

import {
	CollapsablePanelContentContainer,
	CollapsablePanelIcon,
	CollapsablePanelTitle,
} from "./styles/collapsable-panel.styled";

type Props = {
	children: ReactNode;
	id: string;
	initialOpen: boolean;
	label: string;
	reference: string;
};
function CollapsablePanel({
	children,
	id,
	initialOpen = true,
	label,
	reference,
}: Props) {
	const [open, setOpen] = useState(initialOpen);

	return (
		<>
			<CollapsablePanelTitle as="h3">
				<input
					aria-controls={`${reference}-box-${id}`}
					aria-expanded={open}
					checked={open}
					data-collapse={true}
					id={`${reference}-controller-box-${id}`}
					name={`${reference}-controller`}
					onChange={() => {
						setOpen(!open);
					}}
					role="tab"
					tabIndex={0}
					type="checkbox"
					value={id}
				/>
				<label htmlFor={`${reference}-controller-box-${id}`}>
					{label}
					<CollapsablePanelIcon open={open} />
				</label>
			</CollapsablePanelTitle>
			<CollapsablePanelContentContainer
				aria-hidden={!open}
				id={`${reference}-box-${id}`}
				open={open}
				role="tabpanel"
			>
				{children}
			</CollapsablePanelContentContainer>
		</>
	);
}

export default CollapsablePanel;

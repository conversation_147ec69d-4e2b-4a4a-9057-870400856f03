import { ButtonTertiary } from "@anwb/poncho/components/button";
import Form from "@anwb/poncho/components/form";
import FormFieldEmail from "@anwb/poncho/components/form-field-email";
import Notification from "@anwb/poncho/components/notification";
import { Panel } from "@anwb/poncho/components/panel";
import Typography from "@anwb/poncho/components/typography";
import { useNewsletterSubscribe } from "@anwb/webshop-helpers";
import { useEffect, useState } from "react";

type FormState = {
	elementsData: {
		emailAddress: string;
	};
	valid: boolean;
};

type NewsletterMessages = {
	error: string;
	incorrectValue: string;
	missingValue: string;
	success: string;
};

export type Props = {
	footnote: null | string;
	messages?: NewsletterMessages;
	text: string;
	title: string;
};

const defaultNewsletterMessages = {
	error: "Er ging iets mis bij het inschrijven, probeer het later nog eens",
	incorrectValue: "Je hebt je e-mailadres onjuist ingevuld.",
	missingValue: "Je hebt je e-mailadres nog niet ingevuld.",
	success: "Je voorkeuren zijn gewijzigd en doorgevoerd in onze administratie",
};

export function NewsletterComponent({
	footnote,
	messages = defaultNewsletterMessages,
	text,
	title,
}: Props) {
	const { loading, status, subscribe } = useNewsletterSubscribe();
	const [notification, setNotification] = useState<boolean>(false);

	const handleSubmit = (formData: unknown): void => {
		const form = formData as FormState;
		if (form.valid) {
			void subscribe(form.elementsData.emailAddress);
		}
	};

	useEffect(() => {
		if (status) setNotification(true);
	}, [status]);

	return (
		<Panel variant="featured">
			<Panel.Header headerLevel="h2" title={title} />
			<Panel.Content>
				<div className="WEBSHOP-newsletter__panel-header">
					{status === "success" && (
						<Notification
							closeButton
							onClose={() => {
								setNotification(false);
							}}
							title={messages.success}
							variant="success"
						/>
					)}

					{status === "failed" && (
						<Notification
							closeButton
							onClose={() => {
								setNotification(false);
							}}
							title={messages.error}
							variant="error"
						/>
					)}

					{!notification && (
						<>
							<Typography tagName="span" variant="body-text">
								{text}
							</Typography>
							{footnote && (
								<Typography
									style={{ marginBottom: "1rem", marginTop: "0.5rem" }}
									variant="support-text"
								>
									{footnote}
								</Typography>
							)}
						</>
					)}
				</div>
				<Form loading={loading} onSubmit={handleSubmit}>
					<FormFieldEmail
						colorContext="on-medium"
						label="E-mailadres"
						name="emailAddress"
						reference="emailAddress"
						required
						size="xl"
						textValidationMessages={{
							patternMismatch: messages.incorrectValue,
							typeMismatch: messages.incorrectValue,
							valueMissing: messages.missingValue,
						}}
					/>

					<ButtonTertiary type="submit">Inschrijven</ButtonTertiary>
				</Form>
			</Panel.Content>
		</Panel>
	);
}

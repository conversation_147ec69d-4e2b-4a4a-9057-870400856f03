import type { FooterDocumentData } from "@anwb/webshop-prismic";

import Grid from "@anwb/poncho/components/grid";
import Pane from "@anwb/poncho/components/pane";
import {
	Panel,
	PanelContact,
	PanelContent,
	PanelHeader,
} from "@anwb/poncho/components/panel";
import { asLink, asText, isFilled } from "@prismicio/client";

import { NewsletterComponent } from "./components/Newsletter";
import * as Styled from "./Styles";

export function Footer(content: FooterDocumentData) {
	return (
		<Styled.Footer className="WEBSHOP-footer WEBSHOP">
			<Grid
				columns={{
					lg: 4,
					md: 2,
					sm: 1,
				}}
				constrainedWidth={true}
				equalHeight={true}
			>
				<Grid.Item>
					<PanelContact>
						<PanelContact.Header
							headerLevel="h2"
							title={asText(content.block_1_title)}
						></PanelContact.Header>
						{content.block_1_content.map((linkField) => (
							<>
								<PanelContact.Divider></PanelContact.Divider>
								<PanelContact.Row>
									<Styled.Link
										href={asLink(linkField.link) || undefined}
										key={linkField.link_text}
									>
										{linkField.link_text}
									</Styled.Link>
								</PanelContact.Row>
							</>
						))}
					</PanelContact>
				</Grid.Item>
				<Grid.Item>
					<PanelContact>
						<PanelContact.Header
							headerLevel="h2"
							title={asText(content.block_2_title)}
						></PanelContact.Header>
						{content.block_2_content.map((linkField) => (
							<>
								<PanelContact.Divider></PanelContact.Divider>
								<PanelContact.Row>
									<Styled.Link
										href={asLink(linkField.link) || undefined}
										key={linkField.link_text}
									>
										{linkField.link_text}
									</Styled.Link>
								</PanelContact.Row>
							</>
						))}
					</PanelContact>
				</Grid.Item>
				<Grid.Item>
					<NewsletterComponent
						footnote={content.block_3_footnote}
						messages={{
							error: content.block_3_error ?? "",
							incorrectValue: content.block_3_incorrect_value ?? "",
							missingValue: content.block_3_missing_value ?? "",
							success: content.block_3_success ?? "",
						}}
						text={content.block_3_text ?? ""}
						title={asText(content.block_3_title)}
					/>
				</Grid.Item>
				<Grid.Item>
					<Panel className="WEBSHOP-footer__payment-options" variant="featured">
						<PanelHeader
							className="WEBSHOP-footer__title"
							headerLevel="h2"
							title={asText(content.block_4_title)}
						/>
						<PanelContent>
							<Pane className="WEBSHOP-footer__icon-list">
								{content.block_4_content.map((iconData) => (
									<a
										className={
											isFilled.link(iconData.icon) &&
											iconData.icon.name === "icon-anwb.png"
												? "WEBSHOP-footer__icon-list--hide-icon"
												: ""
										}
										href={asLink(iconData.link) || undefined}
										key={iconData.icon.text}
									>
										<Pane className="WEBSHOP-footer__payment-icon-container">
											<img
												alt={asText(iconData.icon_alt_text)}
												height="20"
												key={iconData.icon.text}
												loading="lazy"
												src={
													isFilled.link(iconData.icon)
														? iconData.icon.url
														: undefined
												}
												width="40"
											/>
										</Pane>
									</a>
								))}
							</Pane>
						</PanelContent>
					</Panel>
				</Grid.Item>
			</Grid>
		</Styled.Footer>
	);
}

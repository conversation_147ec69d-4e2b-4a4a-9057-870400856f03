import {
	L2CategoryList,
	L2CustomCategoryList,
	L4CategoryList,
} from "./attribute-set-data";

export const getL2Category = (productGroup?: null | string) => {
	if (!productGroup) return null;

	if (L2CustomCategoryList[productGroup]) {
		return L2CustomCategoryList[productGroup];
	}

	const attributeSetNumber = productGroup.split(" ")[0];
	// @ts-expect-error todo: fix this type
	const l2FromAttributeSetNumber = attributeSetNumber.slice(0, 3);
	const l2Value = L2CategoryList[l2FromAttributeSetNumber];

	return l2Value || null;
};

export const getL4Category = (productGroup?: null | string) => {
	if (!productGroup) return null;

	const attributeSetNumber = productGroup.split(" ")[0];
	// @ts-expect-error todo: fix this type
	const l4FromAttributeSetNumber = attributeSetNumber.slice(0, 5);
	const l4Value = L4CategoryList[l4FromAttributeSetNumber];
	return l4Value || null;
};

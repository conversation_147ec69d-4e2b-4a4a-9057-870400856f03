import type { StickerProps } from "@anwb/poncho/components/sticker/next";
import type { IllustrativeIconsKey } from "@anwb/poncho/utilities/icon-utils";
import type {
	CategoryHeaderContent,
	FeaturedProductsArgs,
	PrismicBannerWidth,
	PrismicContent,
	PrismicResponsiveImage,
} from "@anwb/webshop-types";
import type { RichTextBlock } from "prismic-reactjs";

import { generateImageURL } from "../misc";

const makeCtaLink = (item: PrismicContent): string | undefined => {
	// NOTE order is import as content editors may have filled cate ID and a lin
	if (item.category_id) {
		return `/webwinkel/c/${item.category_id}`;
	}

	if (item.cta_link && item.cta_link.link_type === "Document") {
		return `/webwinkel/${item.cta_link.slug}`;
	}

	if (item.cta_link && item.cta_link.link_type === "Web") {
		return item.cta_link.url;
	}

	return undefined;
};

type BannerWidth = "full" | "half" | "quarter" | "three-quarters";
const selectBannerWidthClassModifier = (
	bannerWidth: PrismicBannerWidth,
): BannerWidth | null => {
	if (!bannerWidth) return null;
	switch (bannerWidth) {
		case "1/2":
			return "half";
		case "1/4":
			return "quarter";
		case "3/4":
			return "three-quarters";
		case "Volledige breedte":
			return "full";
		default:
			return "half";
	}
};

export const selectCategoryHeaderContent = (
	data: null | PrismicContent,
): CategoryHeaderContent | null => {
	if (!data) return null;
	return {
		bigDescription: data.big_description?.[0]?.text
			? data.big_description
			: null,
		description: data.description,
		headerButtons: data.header_buttons?.length
			? data.header_buttons
					// @ts-expect-error todo: fix this type
					.map((button) => {
						const { button_label: label, button_url: url } = button;
						if (!label || !url) return;
						return { label, url };
					})
					// @ts-expect-error todo: fix this type
					.filter((button) => button)
			: [],
		image: data.image,
		title: data.title,
	};
};

export const selectRowContent = (slice: PrismicContent) => ({
	rowType: slice.row_type,
	title: slice.title,
});

export type selectBannerContentType = {
	ctaLabel?: string;
	ctaLink?: string;
	description: Array<RichTextBlock>;
	image: PrismicResponsiveImage;
	inverted: boolean;
	labelText: string;
	labelType: string;
	labelValue: string;
	title: string;
	width: BannerWidth;
};
export const selectBannerContent = (
	slice: PrismicContent,
): selectBannerContentType => ({
	...(slice.banner_type && { rowType: slice.banner_type }),
	ctaLabel: slice.cta_label,
	ctaLink: makeCtaLink(slice),
	description: slice.description,
	gradientOverlay: slice.gradient_overlay,
	image: slice.image_banner,
	inverted: slice.inverted,
	labelText: slice.label_text,
	labelType: slice.label_type,
	labelValue: slice.label_value,
	title: slice.title,
	width: selectBannerWidthClassModifier(slice.width),
});

export const selectImageBanners = (data: null | PrismicContent) => {
	if (data && data.body) {
		return data.body
			.filter((item: any) => item.slice_type === "banner_afbeelding")
			.map((item: any) => selectBannerContent(item.primary));
	}
	return [];
};

export const selectImageBanner = (productCatSlice: any | null) => {
	if (!productCatSlice) return null;
	return selectBannerContent(productCatSlice);
};

export const selectFeaturedProductInput = (
	featuredProductSlice: any | null,
): FeaturedProductsArgs | null => {
	if (!featuredProductSlice) return null;
	return {
		templateId:
			(featuredProductSlice?.tweakwise_id &&
				Number(featuredProductSlice?.tweakwise_id)) ||
			null,
		title: featuredProductSlice?.title?.[0]?.text,
	};
};

export type NavigationTiles = Array<{
	alt: null | string;
	imgSrc: null | string;
	link: null | string;
	title: null | string;
}>;

export type selectNavigationTilesType = {
	hidden: boolean;
	hideTitle: boolean;
	tiles: NavigationTiles;
	title: null | string;
};

const mapNavigationTiles = (items: any) => {
	return items
		? items.map((item: any) => ({
				alt: item.image_alt_text?.[0]?.text || null,
				imgSrc: item.image_source?.[0]?.text || null,
				link: item.link?.[0]?.text || null,
				title: item.title?.[0]?.text || null,
			}))
		: [];
};

export const selectNavigationTiles = (
	items?: Array<any>,
	primary?: any,
): selectNavigationTilesType => {
	return {
		hidden: primary.hidden ?? false,
		hideTitle: primary.hide_title || false,
		tiles: mapNavigationTiles(items),
		title: primary.title?.[0]?.text || null,
	};
};

export type Label = {
	text?: string;
	variant?: null | StickerProps["variant"];
};

export type selectSpotlightType = {
	cards: Array<{
		hasSticker: boolean;
		href: string;
		imageSrc: string;
		labels?: Array<Label>;
		lines: [] | Array<string>;
		linkText: string;
	}>;
	title: string;
};

export const selectSpotlight = (
	primary: any,
	items?: Array<any>,
): selectSpotlightType => ({
	cards: items
		? items.map((item) => ({
				hasSticker: item.promotional_sticker || false,
				href: item.link[0]?.text as string,
				imageSrc: item.image_source[0].text as string,
				labels: (() => {
					const labels = [];
					if (item.label1_text?.[0]?.text || item.label1_value) {
						labels.push({
							text: item.label1_text?.[0]?.text || null,
							variant: item.label1_value ?? null,
						});
					}
					if (item.label2_text?.[0]?.text || item.label2_value) {
						labels.push({
							text: item.label2_text?.[0]?.text || null,
							variant: item.label2_value ?? null,
						});
					}
					return labels;
				})(),
				lines: (() => {
					const lines = [];
					if (item.sticker_line_1?.[0]?.text) {
						lines.push(item.sticker_line_1[0].text);
					}
					if (item.sticker_line_2?.[0]?.text) {
						lines.push(item.sticker_line_2[0].text);
					}
					if (item.sticker_line_3?.[0]?.text) {
						lines.push(item.sticker_line_3[0].text);
					}
					return lines;
				})(),
				linkText: item.title[0].text as string,
			}))
		: [],
	title: primary?.title?.[0]?.text || "",
});

export type Card = {
	href: string;
	iconVariant: IllustrativeIconsKey;
	imageAlt: string;
	imageSrc: string;
	labels: Array<Label>;
	target: string;
	title: string;
};

export type selectCollectionViewType = {
	cards: Array<Card>;
	description: string;
	orientation: "landscape" | "portrait";
	title: string;
};

export const selectCollectionView = (
	primary: any,
	items?: Array<any>,
): selectCollectionViewType => ({
	cards: items
		? items.map((item) => ({
				href: item.link[0]?.text as string,
				iconVariant: item.icon_variant as IllustrativeIconsKey,
				imageAlt: item.image_alt[0]?.text as string,
				imageSrc: item.image_source[0]?.text as string,
				labels: [
					{
						text: item.label_text[0]?.text,
						variant: item.label_variant
							? item.label_variant.toLowerCase()
							: null,
					},
				],
				target: item.target as string,
				title: item.title[0]?.text as string,
			}))
		: [],
	description: primary?.description?.[0]?.text || "",
	orientation: primary?.orientation as "landscape" | "portrait",
	title: primary?.title?.[0]?.text || "",
});

export type selectShopHeaderType = {
	cards: Array<Card>;
	orientation: "landscape" | "portrait";
	title: string;
	visible: boolean;
};

export const selectShopHeader = (
	primary: any,
	items?: Array<any>,
): selectShopHeaderType => ({
	cards: items
		? items.map((item) => ({
				href: item.link[0]?.text as string,
				iconVariant: item.icon_variant as IllustrativeIconsKey,
				imageAlt: item.image_alt[0]?.text as string,
				imageSrc: item.image_source[0]?.text as string,
				labels: [
					{
						text: item.label_text[0]?.text,
						variant: item.label_variant
							? item.label_variant.toLowerCase()
							: null,
					},
				],
				target: item.target as string,
				title: item.title[0]?.text as string,
			}))
		: [],
	orientation: primary?.orientation as "landscape" | "portrait",
	title: primary?.title?.[0]?.text || "",
	visible: primary?.visible as boolean,
});

export type Tile = {
	href: string;
	sticky: boolean;
	text: string;
	title: string;
};

export type selectPlazaType = {
	actionTile: Tile;
	bottomImage: string;
	title: string;
	topLeftImage: string;
	topRightImage: string;
	type: "plein" | "subplein";
};

export const selectPlaza = (primary: any): selectPlazaType => ({
	actionTile: {
		href: primary?.tile_link?.url || "",
		sticky: primary?.sticky || false,
		text: primary?.tile_text?.[0]?.text || "",
		title: primary?.tile_link_title?.[0]?.text || "",
	},
	bottomImage: primary?.bottom_image?.[0]?.text || "",
	title: primary?.title?.[0]?.text || "",
	topLeftImage: primary?.top_left_image?.[0]?.text || "",
	topRightImage: primary?.top_right_image?.[0]?.text || "",
	type: primary?.type as "plein" | "subplein",
});

export type BlocklistItem = {
	description: string;
	iconVariant: IllustrativeIconsKey;
	title: string;
};

export type selectBlockListItemsType = {
	blockListItems: Array<BlocklistItem>;
};

export const selectBlockListItems = (
	items: Array<any>,
): selectBlockListItemsType => ({
	blockListItems:
		items?.map((item) => ({
			description: item.description as string,
			iconVariant: item.icon as IllustrativeIconsKey,
			title: item.title as string,
		})) || [],
});

export type FormuleHeaderNavigationItem = {
	href: string;
	title: string;
};

export type selectFormuleHeaderType = {
	headerImage?: string;
	introDescription?: string;
	introSubtitle?: string;
	introTitle?: string;
	navigationItems: Array<FormuleHeaderNavigationItem>;
};

export const selectFormuleHeader = (
	primary: any,
	items?: Array<any>,
): selectFormuleHeaderType => ({
	headerImage: primary?.header_image
		? generateImageURL({
				format: "webp",
				url: primary.header_image,
				width: 1800,
			})
		: undefined,
	introDescription: primary?.intro_description,
	introSubtitle: primary?.intro_subtitle,
	introTitle: primary?.intro_title,
	navigationItems: items
		? items.map((item) => ({
				href: item.navigation_item_link,
				title: item.navigation_item_title,
			}))
		: [],
});

export type selectCampaignBannerType = {
	button: {
		link: null | string;
		target: "_blank" | "_self";
		text: null | string;
	};
	hidden: boolean;
	image: {
		default: null | string;
		mobile: null | string;
	};
	stickerLine1: null | string;
	stickerLine2: null | string;
	stickerLine3: null | string;
	text: null | string;
	title: null | string;
};

export const selectCampaignBanner = (
	primary: any,
): selectCampaignBannerType => ({
	button: {
		link: primary?.button_link?.url || null,
		target:
			primary?.button_link?.url === "_blank"
				? primary?.button_link?.url
				: "_self",
		text: primary?.button_text?.[0]?.text || null,
	},
	hidden: primary?.hidden || false,
	image: {
		default: primary?.background_image?.url || "",
		mobile: primary?.mobile_background_image?.url || "",
	},
	stickerLine1: primary?.sticker_line_1?.[0]?.text || null,
	stickerLine2: primary?.sticker_line_2?.[0]?.text || null,
	stickerLine3: primary?.sticker_line_3?.[0]?.text || null,
	text: primary?.campaign_text?.[0]?.text || null,
	title: primary?.campaign_title?.[0]?.text || null,
});

export type selectCampaignSearchType = {
	campaignLink: null | string;
	hidden: boolean;
	image: {
		default: string;
		mobile: string;
	};
	title: null | string;
};

export const selectCampaignSearch = (
	primary: any,
): selectCampaignSearchType => ({
	campaignLink: primary?.campaign_link?.url || null,
	hidden: primary?.hidden ?? false,
	image: {
		default: primary?.background_image?.url || "",
		mobile: primary?.mobile_background_image?.url || "",
	},
	title: primary?.campaign_title[0]?.text || null,
});

export type selectPageBreakerType = {
	buttonLink: string;
	buttonText: string;
	image: string;
	subtitle: string;
	title: string;
};

export const selectPageBreaker = (primary: any): selectPageBreakerType => ({
	buttonLink: primary?.button_link?.url || "",
	buttonText: primary?.button_text || "",
	image: primary?.image || "",
	subtitle: primary?.subtitle || "",
	title: primary?.title || "",
});

export type TopTasksTile = {
	icon: IllustrativeIconsKey;
	link: string;
	text: string;
	variant?: "primary" | "secondary";
};

export type SelectTopTasksType = {
	subTitle: string;
	tiles: Array<TopTasksTile>;
	title: string;
};

export const selectTopTasks = (
	primary: any,
	items?: Array<any>,
): SelectTopTasksType => ({
	subTitle: primary?.subtitle || "",
	tiles: items
		? items.map((item) => ({
				icon: item?.tile_icon || "",
				link: item?.tile_link?.url || "",
				text: item?.tile_text || "",
				variant: item?.tile_variant || primary?.variant || "primary",
			}))
		: [],
	title: primary?.title || "",
});

export type BlockedExplainerItem = {
	icon: IllustrativeIconsKey;
	text: string;
	title: string;
};

export type SelectBlockedExplainerType = {
	items: Array<BlockedExplainerItem>;
	subtitle: string;
	title: string;
};

export const selectBlockedExplainer = (
	primary: any,
	items?: Array<any>,
): SelectBlockedExplainerType => ({
	items: items
		? items.map((item) => ({
				icon: item.icon || "",
				text: item.text || "",
				title: item.title || "",
			}))
		: [],
	subtitle: primary?.subtitle || "",
	title: primary?.title || "",
});

const mapCarouselType = (type: string) => {
	if (!type) return null;

	switch (type.toLowerCase()) {
		case "type 1":
			return 1;
		case "type 2":
			return 2;
		case "type 3":
			return 3;
		default:
			return 1;
	}
};

export type Text = {
	direction: string;
	spans: Array<string>;
	text: string;
	type: string;
};

export type PrismicCampaignType = {
	campaign_block_image: Array<Text>;
	campaign_block_label: string;
	campaign_block_label_text: Array<Text>;
	campaign_block_link: Array<Text>;
	campaign_block_orientation: string;
	campaign_block_title: Array<Text>;
	direction: string;
	spans: Array<string>;
	text: string;
	type: string;
};

export const mapPrismicLabels = (item: string) => {
	switch (item) {
		case "Campagne":
			return "campaign";
		case "Duurzaam":
			return "societal";
		case "Kampioen":
			return "product";
		case "Ledenvoordeel":
			return "member-benefit";
		case "Nieuw":
			return "campaign";
		case "Sale":
			return "promotional";
		case "Uitverkocht":
			return "sold-out";
		default:
			return "member-benefit";
	}
};

export const selectCampaignBlocks = (primary: any, items: any) => {
	if (!items) {
		return null;
	}

	return items.map((item: PrismicCampaignType) => ({
		image: item.campaign_block_image?.[0]?.text || null,
		label: {
			text: item.campaign_block_label_text?.[0]?.text || null,
			type: mapPrismicLabels(item.campaign_block_label),
		},
		link: item.campaign_block_link?.[0]?.text || null,
		orientation: primary.campaign_blocks_orientation ? "portrait" : "landscape",
		title: item.campaign_block_title?.[0]?.text || null,
	}));
};

export type PrismicCarouselType = {
	carousel_block_bg_image: Array<Text>;
	carousel_block_image: Array<Text>;
	carousel_block_label: Array<Text>;
	carousel_block_label_type: string;
	carousel_block_link: Array<Text>;
	carousel_block_title: Array<Text>;
	carousel_block_type: string;
	carousel_text_top_position: string;
};

export const selectCampaignCarousel = (items: any) => {
	if (!items) {
		return null;
	}

	if (items.length > 3) {
		items.splice(3, Infinity);
	}

	return items.map((item: PrismicCarouselType) => ({
		image: item.carousel_block_image?.[0]?.text || null,
		imageBg: item.carousel_block_bg_image?.[0]?.text || null,
		label: {
			text: item.carousel_block_label?.[0]?.text || null,
			type:
				mapCarouselType(item?.carousel_block_type) === 1 &&
				item.carousel_block_label_type !== "Sale"
					? "campaign"
					: mapPrismicLabels(item.carousel_block_label_type), //Fix for design flaw: Card type 1 can only have red or gray labels
		},
		link: item.carousel_block_link?.[0]?.text || null,
		textTopPosition: item.carousel_text_top_position || false,
		title: item.carousel_block_title?.[0]?.text || null,
		type: mapCarouselType(item?.carousel_block_type),
	}));
};

export const selectAutoFilter = (primary: any) => {
	if (!primary) {
		return null;
	}

	return {
		autoFilterTitle: primary.auto_filter_titel?.[0]?.text,
		kentekenFilterTitle: primary.kenteken_filter_titel?.[0]?.text,
	};
};

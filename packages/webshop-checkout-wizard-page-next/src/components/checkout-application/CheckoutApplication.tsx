import {
	AUTHENTICATED,
	useAuthenticationStatus,
} from "@anwb/ciam-authentication-status-provider";
import { BusStop } from "@anwb/poncho/components/bus-stop";
import Container from "@anwb/poncho/components/container";
import Grid from "@anwb/poncho/components/grid";
import Panel from "@anwb/poncho/components/panel";
import Typography, { BodyText } from "@anwb/poncho/components/typography";
import {
	getLocalStorage,
	removeLocalStorage,
	setLocalStorage,
} from "@anwb/poncho/utilities/browser";
import { useEffect } from "react";

import {
	useCartState,
	useCheckoutDispatch,
	useCheckoutState,
} from "../../context";
import loadLocalStoredPersonalInformation from "../../context/checkout/actions/loadLocalStoredPersonalInformation";
import loadMemberData from "../../context/checkout/actions/loadMemberData";
import regenerateCart from "../../context/checkout/actions/regenerateCart";
import { useCheckoutViewLogging } from "../../events";
import { useKeepUrlSynchronizedWithState } from "../../helpers/useKeepUrlSynchronizedWithState";
import DeliveryAndPaymentForm from "../delivery-and-payment-form/DeliveryAndPaymentForm";
import PersonalInformationForm from "../personal-information-form/PersonalInformationForm";
import Receipt from "../receipt/Receipt";
import TermsAndConditionsModal from "../terms-and-conditions-modal/TermsAndConditionsModal";
import { CheckoutApplicationGrid } from "./styles/checkout-application.styled";

function CheckoutApplication() {
	const { currentStep, personalInformation, steps } = useCheckoutState();
	const { items } = useCartState();

	const { status } = useAuthenticationStatus();
	const isLoggedIn = status === AUTHENTICATED;

	const dispatch = useCheckoutDispatch();
	const { pushCheckoutView } = useCheckoutViewLogging();

	const activeStep = steps.find((step) => step.url === currentStep);
	useKeepUrlSynchronizedWithState();

	// Temp local storage key for supporting new and old checkout
	useEffect(() => {
		setLocalStorage("anwb-webshop-checkout-type", "wizard");
	}, []);

	useEffect(() => {
		pushCheckoutView();
	}, []);

	// Try to regenerate cart if cart item list is empty and MaskedID is present in local storage.
	// This occurs when the user goes back to the checkout page after initiating and not finishing a payment.
	useEffect(() => {
		const maskedOrderId = getLocalStorage<null | string>(
			"anwb-webshop-masked-id",
			null,
		);

		if (items.length === 0 && maskedOrderId) {
			regenerateCart({ dispatch, maskedOrderId }).then(() => {
				removeLocalStorage("anwb-webshop-masked-id");
				window.location.reload();
			});
		}
	}, []);

	useEffect(() => {
		const initialLoadFormData = async () => {
			// If there is no billingAddress there is no existing card (see getCartDataAsync)
			// If the user is logged in, we load the memberdata onto the state
			// Else we load the local stored data if any.
			if (!personalInformation.billingAddress) {
				if (isLoggedIn) {
					await loadMemberData({ dispatch });
				} else {
					loadLocalStoredPersonalInformation({ dispatch });
				}
			}
		};

		initialLoadFormData()
			.catch(console.error)
			.finally(() => {
				dispatch({ pending: false, type: "SET_PENDING" });
			});
	}, []);

	return (
		<Container withConstrainedWidth>
			{items.length === 0 && (
				<Panel>
					<Panel.Content>
						<BodyText>Er staan nog geen producten in de winkelmand.</BodyText>
					</Panel.Content>
				</Panel>
			)}
			{items.length > 0 && activeStep && (
				<Container withOuterSpacing="horizontal">
					<Typography as="h1" variant="group-component-title">
						Je bestelling
					</Typography>
					<BusStop>
						{steps.map((step, index) => {
							return (
								<BusStop.Item
									active={step.url === activeStep.url}
									href={
										index < steps.indexOf(activeStep) ? step.href : undefined
									}
								>
									{step.text}
								</BusStop.Item>
							);
						})}
					</BusStop>
					<CheckoutApplicationGrid>
						<Grid.Item>
							{currentStep === "gegevens" && <PersonalInformationForm />}
							{currentStep === "bezorgen" && <DeliveryAndPaymentForm />}
						</Grid.Item>
						<Grid.Aside isSticky>
							<Receipt />
						</Grid.Aside>
					</CheckoutApplicationGrid>
					<TermsAndConditionsModal />
				</Container>
			)}
		</Container>
	);
}

export default CheckoutApplication;

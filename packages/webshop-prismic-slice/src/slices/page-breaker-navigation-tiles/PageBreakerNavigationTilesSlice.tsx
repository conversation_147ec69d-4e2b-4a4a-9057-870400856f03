import type {
	ContentPageDocumentDataBodyPageBreakerNavigationTilesSliceItem as PageBreakersNavigationSlideSliceItem,
	ContentPageDocumentDataBodyPageBreakerNavigationTilesSlicePrimary as PageBreakersNavigationSlideSlicePrimary,
} from "@anwb/webshop-prismic";
import type { RichTextField } from "@prismicio/client";

import NavigationTiles from "@anwb/webshop-component-navigation-tiles";

import PageBreakerHeaderSlice from "../page-breaker-header/PageBreakerHeader";
import { ContentsWrapper } from "./styles/page-breaker-navigation-tiles.styled";

function PageBreakerNavigationTilesSlice({
	items,
	primary,
}: {
	items: Array<PageBreakersNavigationSlideSliceItem>;
	primary: PageBreakersNavigationSlideSlicePrimary;
}) {
	if (!items.length) return null;

	const emptyRichText: RichTextField = [
		{
			spans: [],
			text: "",
			type: "paragraph",
		},
	];
	const itemsNew = {
		hidden: false,
		hide_title: true,
		title: emptyRichText,
	};

	return (
		<>
			<PageBreakerHeaderSlice primary={primary}>
				<ContentsWrapper>
					{items && <NavigationTiles items={items} primary={itemsNew} />}
				</ContentsWrapper>
			</PageBreakerHeaderSlice>
		</>
	);
}

export default PageBreakerNavigationTilesSlice;

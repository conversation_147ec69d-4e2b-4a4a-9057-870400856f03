import type { ContentPageDocumentDataBodyPageBreakerPopularProductsSlicePrimary as PageBreakerPopularProductsPrimary } from "@anwb/webshop-prismic";

import { asText } from "@prismicio/client";

import PageBreakerHeaderSlice from "../page-breaker-header/PageBreakerHeader";
import PrismicSlicePopularProducts from "../popular-products/PrismicSlicePopularProducts";
import { ContentsWrapper } from "./styles/page-breaker-popular-products.styled";

function PageBreakerPopularProductsSlice({
	primary,
}: {
	primary: PageBreakerPopularProductsPrimary;
}) {
	if (!primary) return null;

	const categories = asText(primary.categories)
		.split(",")
		.map((value) => ({
			text: value,
		}));

	return (
		<>
			<PageBreakerHeaderSlice primary={primary}>
				<ContentsWrapper>
					<PrismicSlicePopularProducts
						categories={categories}
						chipsIsVisible={false}
						headerIsVisible={false}
					/>
				</ContentsWrapper>
			</PageBreakerHeaderSlice>
		</>
	);
}

export default PageBreakerPopularProductsSlice;

import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";
import type { ContentPageDocumentDataBodyPageBreakerNavigationTilesSlicePrimary as PageBreakerNavigationTilesType } from "@anwb/webshop-prismic";

import { ButtonTertiary } from "@anwb/poncho/components/button";
import Container from "@anwb/poncho/components/container";
import { BodyText } from "@anwb/poncho/components/typography";
import { GroupComponentTitle } from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const PageBreaker = styled.div.attrs({
	name: "page-breaker-with-subcomponent",
})(
	({ theme }: DefaultThemeProps) => css`
		position: relative;
		display: flex;
		flex-direction: column;
		width: 100%;
		margin-top: ${pxToRem(theme.spacing["500"])};
	`,
);

export const PageBreakerHeader = styled(Container).attrs({
	name: "page-breaker-header",
	withConstrainedWidth: true,
})(
	({
		$variant,
		theme,
	}: {
		$variant: PageBreakerNavigationTilesType["pagebreaker_variant"];
		theme: DefaultThemeProps["theme"];
	}) => css`
		${() => {
			switch ($variant) {
				case "Ocean":
					return OCEAN;
				case "Sun":
				default:
					return SUN;
			}
		}}

		padding: 0 ${pxToRem(theme.spacing["300"])};
		position: relative;

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xs)}) {
			padding: 0 ${pxToRem(theme.spacing["400"])};
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.sm)}) {
			padding: 0 ${pxToRem(theme.spacing["500"])};
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			padding-top: ${pxToRem(theme.spacing["500"])};
		}
		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xl)}) {
			margin-top: calc(2 * ${pxToRem(theme.spacing["600"])});
		}
		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xxl)}) {
			max-width: ${pxToRem(theme.viewportBreakpoint.xl)};
			margin: ${pxToRem(theme.spacing["600"])} auto
				${pxToRem(theme.spacing["600"])} auto;
			padding: ${pxToRem(theme.spacing["400"])} ${pxToRem(theme.spacing["600"])}
				0 ${pxToRem(theme.spacing["600"])};
			box-sizing: content-box;

			border-radius: ${pxToRem(theme.borderRadius.m)}
				${pxToRem(theme.borderRadius.m)} 0 0;
		}

		&:after {
			content: "";
			display: block;
			width: 100%;
			position: absolute;
			height: 28%;
			bottom: -28%;
			left: 0;

			@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xxl)}) {
				max-width: ${pxToRem(theme.viewportBreakpoint.xl)};
				inset: unset;
				box-sizing: content-box;
				padding: 0 ${pxToRem(theme.spacing["600"])};
				border-bottom-left-radius: ${pxToRem(theme.borderRadius.m)};
				border-bottom-right-radius: ${pxToRem(theme.borderRadius.m)};
				height: 88%;
				bottom: -88%;
			}
		}
	`,
);

export const PageBreakerContent = styled.div.attrs({
	name: "page-breaker-content",
})(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		position: relative;
		gap: ${pxToRem(theme.spacing[300])};
		z-index: 1;
		margin: 0;
		padding: 0;
		flex-direction: column;

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.sm)}) {
			align-items: start;
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			flex-direction: row;
			justify-content: space-between;
		}
	`,
);

export const PageBreakerTitle = styled(GroupComponentTitle).attrs({
	name: "page-breaker-title",
})``;

export const PageBreakerWrapper = styled.div.attrs({
	name: "page-breaker-wrapper",
})(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		flex-direction: column;
		order: 2;

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.sm)}) {
			align-items: start;
		}
		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			flex-grow: 1;
			order: 1;
			max-width: 45%;
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xl)}) {
			max-width: ${pxToRem(484)};
			flex-grow: 0;
			margin-left: -${pxToRem(theme.spacing["300"])};
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xxl)}) {
			max-width: ${pxToRem(517)};
			margin-left: unset;
		}
	`,
);

export const Description = styled(BodyText).attrs({
	name: "page-breaker-description",
})(
	({ theme }: DefaultThemeProps) => css`
		margin-bottom: ${pxToRem(theme.spacing["500"])};
	`,
);

export const Image = styled.img.attrs({
	name: "page-breaker-image",
})(
	({ theme }: DefaultThemeProps) => css`
		margin-bottom: ${pxToRem(theme.spacing["300"])};
		border-radius: ${pxToRem(theme.borderRadius.m)};
		aspect-ratio: 5/3;
		order: 1;
		position: relative;

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			width: 100%;
			max-width: 45%;
			flex-grow: 1;
			order: 2;
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.lg)}) {
			max-width: 45%;
		}

		@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xl)}) {
			max-width: ${pxToRem(627)}; //Image has custom max width
			position: absolute;
			right: 0;
			top: -${pxToRem(theme.spacing["800"])};
		}
	`,
);

export const Button = styled(ButtonTertiary).attrs({
	icon: "arrow-right",
	name: "page-breaker-button",
})``;

const SUN = ({ theme }: DefaultThemeProps) => css`
	background: linear-gradient(
			to bottom,
			white,
			white 19%,
			${theme.colors.accent.bgProductBar} 19%,
			${theme.colors.accent.bgProductBar}
		)
		no-repeat 0 0%;

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xxs)}) {
		background: linear-gradient(
				to bottom,
				white,
				white 26%,
				${theme.colors.accent.bgProductBar} 26%,
				${theme.colors.accent.bgProductBar}
			)
			no-repeat 0 0%;
	}

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xs)}) {
		background: linear-gradient(
				to bottom,
				white,
				white 36%,
				${theme.colors.accent.bgProductBar} 36%,
				${theme.colors.accent.bgProductBar}
			)
			no-repeat 0 0%;
	}

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.sm)}) {
		background: linear-gradient(
				to bottom,
				white,
				white 42%,
				${theme.colors.accent.bgProductBar} 42%,
				${theme.colors.accent.bgProductBar}
			)
			no-repeat 0 0%;
	}

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
		background: ${theme.colors.accent.bgProductBar};
	}

	&:after {
		background-color: ${theme.colors.accent.bgProductBar};
	}

	${PageBreakerTitle} {
		color: ${theme.colors.base.textTitles};
	}
	${Description} {
		color: ${theme.colors.base.textTitles};
	}
	${Button} {
		color: ${theme.colors.base.textTitles};
		border-color: ${theme.colors.base.textTitles};

		&:hover {
			background-color: ${theme.colors.gradients.bgButtonTertiaryDecideHover};
		}
	}
`;

const OCEAN = ({ theme }: DefaultThemeProps) => css`
	background: linear-gradient(
			to bottom,
			white,
			white 19%,
			${theme.colors.base.bgBlocksPrimary} 19%,
			${theme.colors.base.bgBlocksPrimary}
		)
		no-repeat 0 0%;

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xxs)}) {
		background: linear-gradient(
				to bottom,
				white,
				white 26%,
				${theme.colors.base.bgBlocksPrimary} 26%,
				${theme.colors.base.bgBlocksPrimary}
			)
			no-repeat 0 0%;
	}

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.xs)}) {
		background: linear-gradient(
				to bottom,
				white,
				white 36%,
				${theme.colors.base.bgBlocksPrimary} 36%,
				${theme.colors.base.bgBlocksPrimary}
			)
			no-repeat 0 0%;
	}

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.sm)}) {
		background: linear-gradient(
				to bottom,
				white,
				white 42%,
				${theme.colors.base.bgBlocksPrimary} 42%,
				${theme.colors.base.bgBlocksPrimary}
			)
			no-repeat 0 0%;
	}

	@media screen and (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
		background: ${theme.colors.base.bgBlocksPrimary};
	}
	&:after {
		background-color: ${theme.colors.base.bgBlocksPrimary};
	}

	${PageBreakerTitle} {
		color: ${theme.colors.blanc.textInverse};
	}
	${Description} {
		color: ${theme.colors.blanc.textInverse};
	}
	${Button} {
		color: ${theme.colors.blanc.textInverse};
		border-color: ${theme.colors.blanc.textInverse};

		&:hover {
			background-color: ${theme.colors.base.bgBlocksPrimaryHover};
		}
	}
`;

import type { ContentPageDocumentDataBodyPageBreakerNavigationTilesSlicePrimary as PageBreakersNavigationSlideSlicePrimary } from "@anwb/webshop-prismic";
import type { PropsWithChildren } from "react";

import { asText } from "@prismicio/client";
import { isFilled } from "@prismicio/client";

import {
	Button,
	Description,
	Image,
	PageBreaker,
	PageBreakerContent,
	PageBreakerHeader,
	PageBreakerTitle,
	PageBreakerWrapper,
} from "./styles/page-breaker-header-slice.styled";

type Props = PropsWithChildren<{
	primary: PageBreakersNavigationSlideSlicePrimary;
}>;

function PageBreakerHeaderSlice({ children, primary }: Props) {
	if (!primary) return null;

	return (
		<>
			<PageBreaker>
				<PageBreakerHeader $variant={primary.pagebreaker_variant}>
					<PageBreakerContent>
						<PageBreakerWrapper>
							{isFilled.richText(primary.pagebreaker_title) && (
								<PageBreakerTitle>
									{asText(primary.pagebreaker_title)}
								</PageBreakerTitle>
							)}
							{isFilled.richText(primary.pagebreaker_description) && (
								<Description>
									{asText(primary.pagebreaker_description)}
								</Description>
							)}
							{isFilled.richText(primary.pagebreaker_button_text) &&
								primary.pagebreaker_button_link && (
									<Button href={asText(primary.pagebreaker_button_link)}>
										{asText(primary.pagebreaker_button_text)}
									</Button>
								)}
						</PageBreakerWrapper>
						{isFilled.richText(primary.pagebreaker_image) && (
							<Image
								alt={asText(primary.pagebreaker_image_alt)}
								src={asText(primary.pagebreaker_image)}
							/>
						)}
					</PageBreakerContent>
				</PageBreakerHeader>
				{children}
			</PageBreaker>
		</>
	);
}

export default PageBreakerHeaderSlice;

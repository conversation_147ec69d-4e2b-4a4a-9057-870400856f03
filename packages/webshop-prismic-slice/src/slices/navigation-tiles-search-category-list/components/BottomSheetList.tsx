import BottomSheet from "@anwb/poncho/components/bottom-sheet";

import type { CategoryListContentProps } from "./CategoryListContent";

export function BottomSheetList({
	children,
	handleCloseSheet,
}: CategoryListContentProps) {
	return (
		<BottomSheet onClose={handleCloseSheet} open={true}>
			<BottomSheet.Header>
				<BottomSheet.Title>Alle categorieën</BottomSheet.Title>
			</BottomSheet.Header>
			<BottomSheet.Body showFullHeight={false}>{children}</BottomSheet.Body>
			<BottomSheet.Header>
				<>
					<BottomSheet.LinkButton onClick={handleCloseSheet}>
						Sluiten
					</BottomSheet.LinkButton>
					<BottomSheet.Title>Alle categorieën</BottomSheet.Title>
					<BottomSheet.CloseButton />
				</>
			</BottomSheet.Header>
		</BottomSheet>
	);
}

import type { PropsWithChildren } from "react";

import { useApplicationSize } from "@anwb/poncho/providers/providers-application";

import { BottomSheetList } from "./BottomSheetList";
import { ModalList } from "./ModalList";

export type CategoryListContentProps = PropsWithChildren<{
	handleCloseSheet: () => void;
}>;

export function CategoryListContent(props: CategoryListContentProps) {
	const applicationSize = useApplicationSize();
	const isLarge = applicationSize === "large";

	if (isLarge) {
		return <ModalList {...props} />;
	}

	return <BottomSheetList {...props} />;
}

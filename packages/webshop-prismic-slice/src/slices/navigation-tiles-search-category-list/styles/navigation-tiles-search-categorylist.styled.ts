import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import { ButtonSecondary } from "@anwb/poncho/components/button";
import {
	BodyText,
	GroupComponentTitle,
} from "@anwb/poncho/components/typography";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

export const NavigationTilesCategoryList = styled.div.attrs({
	name: "page-breaker-with-subcomponent",
})(
	({ theme }: DefaultThemeProps) => css`
		display: flex;
		flex-direction: column;
		justify-content: center;

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			flex-direction: column;
		}
	`,
);

export const Title = styled(GroupComponentTitle).attrs({
	name: "page-breaker-with-subcomponent-title",
})(
	({ theme }: DefaultThemeProps) => css`
		order: 0;
		margin-bottom: ${pxToRem(theme.spacing["400"])};
	`,
);

export const ContentWrapper = styled.div.attrs({
	name: "page-breaker-with-subcomponent-content-wrapper",
})(
	({ theme }: DefaultThemeProps) => css`
		display: block;
		margin-bottom: ${pxToRem(theme.spacing["500"])};

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			display: flex;
			order: 1;
			row-gap: ${pxToRem(theme.spacing["500"])};
			column-gap: ${pxToRem(theme.spacing["300"])};
			justify-content: space-between;
			flex-wrap: wrap;
		}
		@media (min-width: ${pxToRem(theme.viewportBreakpoint.lg)}) {
			column-gap: ${pxToRem(theme.spacing["300"])};
		}
	`,
);

export const SearchBarWrapper = styled.div.attrs({
	name: "page-breaker-with-subcomponent-searchbar-wrapper",
})(
	({ theme }: DefaultThemeProps) => css`
		order: 1;
		position: relative;
		min-height: ${pxToRem(64)};

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			flex-grow: 2;

			& > * {
				position: absolute;
				z-index: ${theme.zIndex.flyOut};
				width: 100%;
				max-width: ${pxToRem(627)};
			}
		}
	`,
);

export const CategoryListButton = styled(ButtonSecondary)(
	({ theme }: DefaultThemeProps) => css`
		order: 4;
		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			order: 3;
			min-height: ${pxToRem(64)};
			height: auto;
		}
	`,
);

export const CategoryNavigationTiles = styled.div(
	({ theme }: DefaultThemeProps) => css`
		order: 3;

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			order: 4;
		}
	`,
);

export const CategoryList = styled.div.attrs({
	name: "page-breaker-with-subcomponent-categorylist",
})(
	({ theme }: DefaultThemeProps) => css`
		grid-area: category-list-content;
		display: grid;
		flex-direction: column;
		gap: ${pxToRem(theme.spacing["100"])};

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.md)}) {
			display: grid;
			grid-template-columns: 1fr;
		}

		@media (min-width: ${pxToRem(theme.viewportBreakpoint.lg)}) {
			grid-template-columns: 1fr 1fr;
		}
	`,
);

export const CategoryLink = styled(BodyText).attrs({
	name: "page-breaker-with-subcomponent-category-link",
})(
	({ theme }: DefaultThemeProps) => css`
		color: ${theme.colors.base.textTitleLink};
		width: 100%;
		display: block;
	`,
);

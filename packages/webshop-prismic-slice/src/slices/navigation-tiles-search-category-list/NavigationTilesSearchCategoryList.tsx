import { useApplicationSize } from "@anwb/poncho/providers/providers-application";
import NavigationTiles from "@anwb/webshop-component-navigation-tiles";
import {
	type AllDocumentTypes,
	type BottomSheetMenuDocument,
	type NavigationTilesDocument,
	type ContentPageDocumentDataBodyNavigationTilesSearchCategoriesSlicePrimary as NavigationTilesSearchCategoryListTypes,
} from "@anwb/webshop-prismic";
import { createPrismicClient } from "@anwb/webshop-prismic/client";
import { asText, isFilled } from "@prismicio/client";
import { queryOptions, useQuery } from "@tanstack/react-query";
import { useMemo, useState } from "react";

import SearchBar from "../searchbar/SearchBar";
import { CategoryListContent } from "./components/CategoryListContent";
import {
	CategoryLink,
	CategoryList,
	CategoryListButton,
	CategoryNavigationTiles,
	ContentWrapper,
	NavigationTilesCategoryList,
	SearchBarWrapper,
	Title,
} from "./styles/navigation-tiles-search-categorylist.styled";

function getSlicesFromPrimarySlice(
	slice: NavigationTilesSearchCategoryListTypes,
) {
	return Object.values(slice)
		.map((item) => item?.id)
		.filter((id) => id !== undefined) as Array<string>;
}

function prismicSlicesQueryOptions(slices: Array<string>) {
	return queryOptions({
		queryFn: async () => prismic.getByIDs(slices),
		queryKey: [WEBSHOP_QUERY_KEY, "prismic", "get-by-id", slices.join("-")],
	});
}

function isNavigationTilesDocument(
	document: AllDocumentTypes,
): document is NavigationTilesDocument {
	return document.type === "navigation_tiles" && !!document.data;
}

function isBottomSheetMenuDocument(
	document: AllDocumentTypes,
): document is BottomSheetMenuDocument {
	return document.type === "bottom_sheet_menu" && !!document.data;
}

const prismic = createPrismicClient();

const WEBSHOP_QUERY_KEY = "webshop";

export function NavigationTilesSearchCategoryList({
	primary,
}: {
	primary: NavigationTilesSearchCategoryListTypes;
}) {
	const [isOpenSheet, setIsOpenSheet] = useState<boolean>(false);

	const applicationSize = useApplicationSize();

	const slices = getSlicesFromPrimarySlice(primary);

	const query = useQuery(prismicSlicesQueryOptions(slices));

	const isLarge = useMemo(() => applicationSize === "large", [applicationSize]);

	const navigationTilesData = useMemo(() => {
		return query.data?.results.find(isNavigationTilesDocument)?.data;
	}, [query]);

	const bottomSheetMenuData = useMemo(() => {
		return query.data?.results.find(isBottomSheetMenuDocument)?.data;
	}, [query]);

	const handleOpenSheet = () => {
		setIsOpenSheet(true);
	};

	const handleCloseSheet = () => {
		setIsOpenSheet(false);
	};

	return (
		<NavigationTilesCategoryList>
			{isFilled.richText(navigationTilesData?.title) && (
				<Title>{asText(navigationTilesData.title)}</Title>
			)}
			<ContentWrapper>
				<SearchBarWrapper>
					<SearchBar />
				</SearchBarWrapper>
				{isLarge && bottomSheetMenuData && (
					<CategoryListButton
						icon="menu"
						iconPosition="before"
						onClick={handleOpenSheet}
					>
						Alle categorieën
					</CategoryListButton>
				)}
			</ContentWrapper>
			{navigationTilesData?.navigation_tiles && (
				<CategoryNavigationTiles>
					<NavigationTiles
						items={navigationTilesData?.navigation_tiles}
						primary={{
							hidden: false,
							hide_title: true,
							title: navigationTilesData?.title,
						}}
					/>
				</CategoryNavigationTiles>
			)}
			{!isLarge && bottomSheetMenuData && (
				<CategoryListButton
					icon="menu"
					iconPosition="before"
					onClick={handleOpenSheet}
				>
					Alle categorieën
				</CategoryListButton>
			)}
			{bottomSheetMenuData && isOpenSheet && (
				<CategoryListContent handleCloseSheet={handleCloseSheet}>
					<CategoryList>
						{bottomSheetMenuData?.navigation_item?.map((item) => (
							<CategoryLink as="a" href={asText(item.item_link)}>
								{asText(item.item_title)}
							</CategoryLink>
						))}
					</CategoryList>
				</CategoryListContent>
			)}
		</NavigationTilesCategoryList>
	);
}

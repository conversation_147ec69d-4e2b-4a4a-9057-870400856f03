import type {
	ContentPageDocumentDataBodyNavigationTilesSliceItem as NavigationTilesItem,
	ContentPageDocumentDataBodyNavigationTilesSlicePrimary as NavigationTilesSlicePrimary,
} from "@anwb/webshop-prismic";

import Typography from "@anwb/poncho/components/typography";
import NavigationTiles from "@anwb/webshop-component-navigation-tiles";
import { asText, isFilled } from "@prismicio/client";

import { PrismicSliceContainer } from "../../styles/prismic-slice.styled";
import SearchBar from "../searchbar/SearchBar";
import {
	SearchBarContainer,
	SearchBarWrapper,
} from "../searchbar/styles/searchbar-slice.styled";

function NavigationTilesSlice({
	items,
	primary,
}: {
	items: Array<NavigationTilesItem>;
	primary: NavigationTilesSlicePrimary;
}) {
	const { hidden, hide_title: hideTitle, title } = primary;

	if (!items.length) return null;

	const styles = hidden ? { height: 0, overflow: "hidden" } : {};

	return (
		<div data-test="navigation-tiles" style={styles}>
			<PrismicSliceContainer>
				{!hideTitle && isFilled.richText(title) && (
					<>
						<Typography variant="group-component-title">
							{asText(title)}
						</Typography>

						<SearchBarWrapper>
							<SearchBarContainer>
								<SearchBar />
							</SearchBarContainer>
						</SearchBarWrapper>
					</>
				)}
			</PrismicSliceContainer>
			<PrismicSliceContainer>
				<NavigationTiles items={items} primary={primary} />
			</PrismicSliceContainer>
		</div>
	);
}

export default NavigationTilesSlice;

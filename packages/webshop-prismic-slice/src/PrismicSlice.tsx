import type { Card } from "@anwb/webshop-helpers";
import type { ContentPageDocumentDataBodySlice } from "@anwb/webshop-prismic";
import type { ContentPageDocumentDataBodyNavigationTilesSliceItem as NavigationTilesItem } from "@anwb/webshop-prismic";
import type { RichTextBlock } from "prismic-reactjs";

import { Accordion } from "@anwb/poncho/components/accordion";
import { BlockList } from "@anwb/poncho/components/block-list";
import { ButtonSecondary } from "@anwb/poncho/components/button";
import Icon from "@anwb/poncho/components/icon";
import Pane from "@anwb/poncho/components/pane";
import { StickerNext } from "@anwb/poncho/components/sticker";
import Typography from "@anwb/poncho/components/typography";
import VisibilitySensor from "@anwb/poncho/components/visibility-sensor";
import { CollectionCard } from "@anwb/poncho/poc/collection-grid";
import ShopHeader from "@anwb/poncho/poc/shop-header";
import {
	BannerWebshop,
	BannerWebshopImage,
	BannerWebshopText,
	BannerWebshopTitle,
} from "@anwb/webshop-banner";
import {
	pushEventPromotionClick,
	pushEventPromotionImpression,
} from "@anwb/webshop-data-layer";
import {
	generateImageURL,
	RichText,
	selectAutoFilter,
	selectBannerContent,
	selectBlockedExplainer,
	selectBlockListItems,
	selectCampaignBanner,
	selectCampaignBlocks,
	selectCampaignCarousel,
	selectCampaignSearch,
	selectCollectionView,
	selectFeaturedProductInput,
	selectFormuleHeader,
	selectPageBreaker,
	selectPlaza,
	selectRowContent,
	selectShopHeader,
	selectSpotlight,
	selectTopTasks,
} from "@anwb/webshop-helpers";
import Plaza from "@anwb/webshop-plaza";
import ProductBannerComponent from "@anwb/webshop-product-banner";
import ProductLabelComponent from "@anwb/webshop-product-label";
import { ProductRecommendations } from "@anwb/webshop-product-recommendations";
import { ProductRecommender } from "@anwb/webshop-product-recommender";
import UspBar from "@anwb/webshop-usp-bar";

import { useCardEventLogging, useComponentVisbilityLogging } from "./events";
import PrismicSliceAccordion from "./PrismicSliceAccordion";
import { FormuleHeader, PrismicSlicePopularProducts } from "./slices";
import BlockedExplainerSlice from "./slices/blocked-explainer/BlockedExplainer";
import { CampaignBanner } from "./slices/campaign-banner/CampaignBanner";
import CampaignBlocks from "./slices/campaign-blocks/CampaignBlocks";
import CampaignCarousel from "./slices/campaign-carousel/CampaignCarousel";
import CampaignSearch from "./slices/campaign-search/CampaignSearch";
import CarFilter from "./slices/car-filter/CarFilter";
import { CollectionViewSlice } from "./slices/collection-view";
import FullWidthImage from "./slices/full-width-image/FullWidthImage";
import { NavigationTilesSearchCategoryList } from "./slices/navigation-tiles-search-category-list/NavigationTilesSearchCategoryList";
import NavigationTilesSlice from "./slices/navigation-tiles/NavigationTilesSlice";
import PageBreakerNavigationTilesSlice from "./slices/page-breaker-navigation-tiles/PageBreakerNavigationTilesSlice";
import PageBreakerPopularProductsSlice from "./slices/page-breaker-popular-products/PageBreakerPopularProductSlice";
import PageBreaker from "./slices/page-breaker/PageBreaker";
import SearchBar from "./slices/searchbar/SearchBar";
import {
	SearchBarContainer,
	SearchBarWrapper,
} from "./slices/searchbar/styles/searchbar-slice.styled";
import SpotlightSlice from "./slices/spotlight/Spotlight";
import TopTasksSlice from "./slices/top-tasks/TopTasks";
import { PrismicSliceContainer } from "./styles/prismic-slice.styled";

type Props = {
	dataLayer: {
		column?: number;
		pageId: string;
		row: number;
	};
	items?:
		| Array<NavigationTilesItem>
		| Array<Record<string, unknown>>
		| Array<RichTextBlock>
		| Array<string>;
	primary: any;
	sliceType: ContentPageDocumentDataBodySlice["slice_type"];
};

type ImageDimensions = Record<
	"landscape" | "portrait",
	{ height: number; width: number }
>;

const imageDimensions: ImageDimensions = {
	landscape: {
		height: 227,
		width: 302,
	},
	portrait: {
		height: 387,
		width: 292,
	},
};

type HandlePromotionEventProps = {
	column?: number;
	creative?: string;
	destinationURL?: string;
	name?: string;
	type: "click" | "impression";
};

export function PrismicSlice({ dataLayer, items, primary, sliceType }: Props) {
	const handlePromotionEvent = ({
		column,
		creative,
		destinationURL,
		name,
		type,
	}: HandlePromotionEventProps) => {
		if (type === "click") {
			pushEventPromotionClick({
				creative: creative || "",
				destinationURL,
				id: dataLayer.pageId,
				name: `${dataLayer.pageId} - ${name}`,
				position: column,
				type: "widget",
				widgetRow: dataLayer?.row,
			});
		}

		if (type === "impression") {
			pushEventPromotionImpression({
				component: "banner",
				creative: creative || "",
				destinationURL,
				id: dataLayer.pageId,
				name: `${dataLayer.pageId} - ${name}`,
				position: column,
				text: creative || "Shop nu",
				type: "widget",
				widgetRow: dataLayer?.row,
			});
			pushEventPromotionImpression(
				{
					component: "button",
					creative: creative || "",
					destinationURL,
					id: dataLayer.pageId,
					name: `${dataLayer.pageId} - ${name}`,
					position: column,
					text: creative || "Shop nu",
					type: "widget",
					widgetRow: dataLayer?.row,
				},
				// @ts-expect-error todo: fix this type
				true,
			);
		}
	};

	if (sliceType === "pagina_header") {
		return (
			<PrismicSliceContainer>
				<BannerWebshop
					image={
						primary.header_image && (
							<BannerWebshopImage
								images={{
									default: {
										url: primary.header_image.url,
									},
								}}
							/>
						)
					}
					noPadding
					variant="primary"
					width="full"
				>
					<BannerWebshopTitle>{primary.header_title}</BannerWebshopTitle>
					<BannerWebshopText>{primary.header_text}</BannerWebshopText>
				</BannerWebshop>
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "pagina-inhoud") {
		return (
			<PrismicSliceContainer>
				<RichText render={primary.content_page_title} />
				<RichText render={primary.page_content} />
			</PrismicSliceContainer>
		);
	}

	// Row with banners
	if (sliceType === "banner_rij") {
		if (!items) return null;
		const { rowType, title: rowTitle } = { ...selectRowContent(primary) };
		return (
			<PrismicSliceContainer>
				{rowTitle && <RichText render={rowTitle} />}
				<div style={{ display: "flex", flexWrap: "wrap" }}>
					{items.map((banner, i) => {
						const {
							ctaLabel,
							ctaLink,
							description,
							// @ts-expect-error todo: fix this type
							gradientOverlay,
							image,
							inverted,
							labelText,
							labelType,
							labelValue,
							title,
							width,
						} = {
							// @ts-expect-error todo: fix this type
							...selectBannerContent(banner),
						};

						// Banners
						if (rowType === "banners")
							return (
								<BannerWebshop
									ctaButton={
										inverted ? (
											<ButtonSecondary
												href={ctaLink}
												onClick={() => {
													handlePromotionEvent({
														column: i + 1,
														creative: title || ctaLabel,
														destinationURL: ctaLink,
														name: `banner rij ${dataLayer.row}`,
														type: "click",
													});
												}}
											>
												{ctaLabel || "Shop nu"}
											</ButtonSecondary>
										) : (
											<ButtonSecondary
												href={ctaLink}
												onClick={() => {
													handlePromotionEvent({
														column: i + 1,
														creative: title || ctaLabel,
														destinationURL: ctaLink,
														name: `banner rij ${dataLayer.row}`,
														type: "click",
													});
												}}
												variant="on-dark"
											>
												{ctaLabel || "Shop nu"}
											</ButtonSecondary>
										)
									}
									gradientOverlay={gradientOverlay}
									image={
										<BannerWebshopImage
											images={{
												default: image,
												mobile: image.mobile,
												tablet: image.tablet,
											}}
										/>
									}
									isVisible={() => {
										handlePromotionEvent({
											column: i + 1,
											creative: title || ctaLabel,
											destinationURL: ctaLink,
											name: `banner rij ${dataLayer.row}`,
											type: "impression",
										});
									}}
									key={`${ctaLabel}-${image?.url}-${title}`}
									link={ctaLink}
									reverseColor={inverted}
									variant="home"
									width={width}
								>
									<BannerWebshopTitle>{title}</BannerWebshopTitle>
									<BannerWebshopText>
										<RichText render={description} />
									</BannerWebshopText>
									{labelType && labelText && (
										<ProductLabelComponent
											label={{
												text: labelText,
												type: labelType,
												value: labelValue,
											}}
										/>
									)}
								</BannerWebshop>
							);

						// products
						if (rowType === "products") {
							return (
								<ProductBannerComponent
									description={description}
									// @ts-expect-error todo: fix this type
									image={image}
									key={`${labelText}-${ctaLink}`}
									labelText={labelText}
									labelType={labelType}
									labelValue={labelValue}
									targetUrl={ctaLink}
									title={title}
									width={width}
								/>
							);
						}
						return null;
					})}
				</div>
			</PrismicSliceContainer>
		);
	}

	// Highlighted products
	if (sliceType === "uitgelichte_producten") {
		const { templateId, title } = { ...selectFeaturedProductInput(primary) };

		if (!templateId) return null;

		return (
			<PrismicSliceContainer>
				<div className="WEBSHOP-tweakwise-recommendations">
					<ProductRecommendations
						templateId={templateId}
						title={title}
						type="featured"
					/>
				</div>

				<div
					className="WEBSHOP-recommender-recommendations"
					style={{ height: 0, overflow: "hidden" }}
				>
					<ProductRecommender />
				</div>
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "faq_section") {
		if (!items) return null;
		return (
			<PrismicSliceContainer>
				<Accordion>
					{items.map((item: any) => (
						<PrismicSliceAccordion
							item={item}
							key={`prismic-slice-accordion-${item.question}`}
						/>
					))}
				</Accordion>
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "embedded_content") {
		if (!primary) return null;
		const title = primary.title?.[0]?.text;
		const renderData = () => ({ __html: primary.embedded_content?.[0]?.text });
		return (
			<>
				{title && (
					<Pane left right>
						<Typography variant="content-title">{title}</Typography>
					</Pane>
				)}

				<PrismicSliceContainer>
					<div
						className="WEBSHOP-embedded-content"
						dangerouslySetInnerHTML={renderData()}
					/>
				</PrismicSliceContainer>
			</>
		);
	}

	if (sliceType === "button_link") {
		if (!primary) return null;
		return (
			<PrismicSliceContainer>
				<ButtonSecondary
					href={primary.button_link.url}
					onClick={() => {
						handlePromotionEvent({
							column: 1,
							creative: primary.button_cta,
							destinationURL: primary.button_link.url,
							name: `banner rij ${dataLayer.row}`,
							type: "click",
						});
					}}
					style={{ marginBottom: "1rem" }}
					target={primary.button_link.target}
				>
					{primary.button_cta || "Shop nu"}
				</ButtonSecondary>
			</PrismicSliceContainer>
		);
	}

	// Navigation tiles
	if (sliceType === "navigation_tiles") {
		if (!primary || !items) return null;
		return (
			<NavigationTilesSlice
				items={items as Array<NavigationTilesItem>}
				primary={primary}
			/>
		);
	}

	// Spotlight
	if (sliceType === "spotlight") {
		const { cards, title } = selectSpotlight(primary, items);

		if (!cards.length) return null;

		// @ts-expect-error todo: fix this type
		return <SpotlightSlice cards={cards} title={title} />;
	}

	// Collection view
	if (sliceType === "collection_view") {
		return <CollectionViewSlice {...selectCollectionView(primary, items)} />;
	}

	// USP Bar
	if (sliceType === "usp_bar") {
		return <UspBar />;
	}

	// Shop header
	if (sliceType === "shop_header") {
		const { cards, orientation, title, visible } = selectShopHeader(
			primary,
			items,
		);

		if (!cards.length) return null;

		const { pushCardEvent } = useCardEventLogging();
		const { pushComponentVisibility } = useComponentVisbilityLogging();

		const handleCardClick = (card: Card) => {
			//Fetch Campaign code
			const url = new URL(window.location.href);
			const params = new URLSearchParams(url.search);
			const icpCode = params.get("icp_code");

			const linkTarget = card.href.includes("anwb.nl")
				? "internal link"
				: "outbound link";

			pushCardEvent({
				href: card.href,
				icpCode,
				linkTarget,
				title: card.title,
			});
		};

		const handleVisbility = (isVisible: boolean, title: string) => {
			isVisible &&
				pushComponentVisibility({
					title,
				});
		};

		return (
			<PrismicSliceContainer
				data-name="prismic-slice-shopheader-container"
				style={{ display: visible ? "block" : "none" }}
			>
				<VisibilitySensor
					onChange={(isVisible) => {
						handleVisbility(isVisible, title);
					}}
				>
					<div>
						<ShopHeader>
							<ShopHeader.Title>{title}</ShopHeader.Title>
							<ShopHeader.Content orientation={orientation}>
								{cards.map((card, index) => (
									<CollectionCard
										hasActionTile={false}
										hasZoomEffect={true}
										href={card.href}
										imageSrc={card.imageSrc}
										key={index + card.title}
										onClick={() => {
											handleCardClick(card);
										}}
										orientation={orientation}
										target={card.target}
									>
										{card.iconVariant && (
											<CollectionCard.CardIcon variant={card.iconVariant} />
										)}

										<CollectionCard.Content
											hasActionTile={false}
											orientation={orientation}
										>
											<CollectionCard.StickerContainer>
												{card.labels &&
													card.labels?.map(
														(label) =>
															label.text &&
															label.variant && (
																<StickerNext
																	key={label.text + label.variant}
																	size={"small"}
																	variant={label.variant}
																>
																	{label.text}
																</StickerNext>
															),
													)}
											</CollectionCard.StickerContainer>
											<CollectionCard.Title orientation={orientation}>
												{card.title}
											</CollectionCard.Title>

											<CollectionCard.ActionTile hasActionTile />
										</CollectionCard.Content>
										{card.imageSrc && (
											<CollectionCard.Image
												imageAlt={card.imageAlt}
												imageSrc={generateImageURL({
													url: card.imageSrc,
													...imageDimensions[orientation],
												})}
											/>
										)}
									</CollectionCard>
								))}
							</ShopHeader.Content>
						</ShopHeader>
					</div>
				</VisibilitySensor>
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "popular_products") {
		return (
			<PrismicSliceContainer>
				<PrismicSlicePopularProducts {...primary} />
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "plein") {
		const {
			actionTile,
			bottomImage,
			title,
			topLeftImage,
			topRightImage,
			type,
		} = selectPlaza(primary);

		return (
			<PrismicSliceContainer>
				<Plaza
					actionTile={actionTile}
					images={[
						generateImageURL({
							format: "webp",
							gravity: "center",
							height: 400,
							url: topLeftImage,
							width: 300,
						}),
						generateImageURL({
							format: "webp",
							gravity: "center",
							height: 600,
							url: topRightImage,
							width: 400,
						}),
						generateImageURL({
							format: "webp",
							gravity: "center",
							height: 150,
							url: bottomImage,
							width: 200,
						}),
					]}
					title={title}
					type={type}
					variant="images"
				/>
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "blocklist") {
		if (!items) return null;

		const { blockListItems } = selectBlockListItems(items);

		return (
			<PrismicSliceContainer>
				<BlockList colorContext="on-light">
					{blockListItems.map((blockListItem, index) => (
						<BlockList.Item key={`blocklist-item-${index}`}>
							<BlockList.ItemIcon>
								<Icon
									aria-hidden="true"
									size="jumbo-xl"
									type="illustrative"
									variant={blockListItem.iconVariant}
								/>
							</BlockList.ItemIcon>
							<BlockList.ItemTitle>{blockListItem.title}</BlockList.ItemTitle>
							<BlockList.ItemText>
								{blockListItem.description}
							</BlockList.ItemText>
						</BlockList.Item>
					))}
				</BlockList>
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "formule_header") {
		const formuleHeaderData = selectFormuleHeader(primary, items);

		return <FormuleHeader {...formuleHeaderData} />;
	}

	if (sliceType === "campaign_banner") {
		const campaignBannerData = selectCampaignBanner(primary);

		if (!campaignBannerData.title || !campaignBannerData.button.link)
			return null;

		// @ts-expect-error todo: fix this type
		return <CampaignBanner data={campaignBannerData} />;
	}

	if (sliceType === "campaign_search") {
		const campaignSearchData = selectCampaignSearch(primary);

		if (!campaignSearchData === null) return null;

		return <CampaignSearch data={campaignSearchData} />;
	}

	if (sliceType === "page_breaker") {
		const pageBreakerData = selectPageBreaker(primary);

		return (
			<PrismicSliceContainer>
				<PageBreaker {...pageBreakerData} />
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "top_tasks") {
		const topTasksData = selectTopTasks(primary, items);

		return <TopTasksSlice {...topTasksData} />;
	}

	if (sliceType === "blocked_explainer") {
		const blockedExplainerData = selectBlockedExplainer(primary, items);

		return <BlockedExplainerSlice {...blockedExplainerData} />;
	}

	if (sliceType === "campaign_blocks") {
		const CampaignBlocksData = selectCampaignBlocks(primary, items);

		return (
			<PrismicSliceContainer>
				<CampaignBlocks data={CampaignBlocksData} />
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "full_width_image_header") {
		const image_source = primary.image_source?.[0]?.text;
		if (!image_source) return null;
		return <FullWidthImage src={image_source} />;
	}

	if (sliceType === "campaign_carousel") {
		const data = selectCampaignCarousel(items);

		return (
			<PrismicSliceContainer>
				<CampaignCarousel data={data} />
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "page_breaker_popular_products") {
		return <PageBreakerPopularProductsSlice primary={primary} />;
	}

	if (sliceType === "page_breaker_navigation_tiles") {
		return (
			<PageBreakerNavigationTilesSlice
				items={items as Array<NavigationTilesItem>}
				primary={primary}
			/>
		);
	}

	if (sliceType === "searchbar") {
		return (
			<PrismicSliceContainer>
				<SearchBarWrapper>
					<SearchBarContainer>
						<SearchBar />
					</SearchBarContainer>
				</SearchBarWrapper>
			</PrismicSliceContainer>
		);
	}

	if (sliceType === "auto_filter") {
		const data = selectAutoFilter(primary);

		return <CarFilter data={data} />;
	}

	if (sliceType === "navigation_tiles_search___categories") {
		return (
			<PrismicSliceContainer>
				<NavigationTilesSearchCategoryList primary={primary} />
			</PrismicSliceContainer>
		);
	}

	return <div />;
}

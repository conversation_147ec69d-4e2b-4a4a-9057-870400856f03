{"name": "@anwb/webshop-prismic-slice", "description": "The Prismic slice used in other webshop packages and applications", "version": "0.0.1", "private": true, "type": "module", "main": "dist/index.js", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc --project tsconfig.build.json", "dev": "tsc --project tsconfig.build.json --watch", "lint": "eslint --cache .", "typecheck": "tsc --noEmit"}, "dependencies": {"@anwb/poncho": "4.64.3", "@anwb/webshop-banner": "workspace:*", "@anwb/webshop-component-navigation-tiles": "workspace:*", "@anwb/webshop-component-popular-products": "workspace:*", "@anwb/webshop-data-layer": "workspace:*", "@anwb/webshop-env": "workspace:*", "@anwb/webshop-helpers": "workspace:*", "@anwb/webshop-plaza": "workspace:*", "@anwb/webshop-prismic": "workspace:*", "@anwb/webshop-product-banner": "workspace:*", "@anwb/webshop-product-label": "workspace:*", "@anwb/webshop-product-recommendations": "workspace:*", "@anwb/webshop-product-recommender": "workspace:*", "@anwb/webshop-products-row": "workspace:*", "@anwb/webshop-types": "workspace:*", "@anwb/webshop-usp-bar": "workspace:*", "@prismicio/client": "^7.17.3", "@tanstack/react-query": "^4.40.0", "graphql": "^16.11.0", "graphql-tag": "^2.12.6", "prismic-reactjs": "^1.3.4", "swiper": "^8.4.7"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11"}, "devDependencies": {"@anwb/tools-eslint-config": "workspace:*", "@total-typescript/tsconfig": "^1.0.4", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "eslint": "^9.24.0", "react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11", "typescript": "^5.8.3"}}
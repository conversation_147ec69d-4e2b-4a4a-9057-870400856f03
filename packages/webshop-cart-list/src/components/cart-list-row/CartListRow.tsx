import Grid from "@anwb/poncho/components/grid";
import Typography from "@anwb/poncho/components/typography";
import {
	type CartItem,
	formatDateToDutch,
	getCartItemErrorMessages,
	INSUFFICIENT_STOCK_ERROR_CODE,
	OUT_OF_STOCK_ERROR_CODE,
	QUANTITY_AVAILABLE_ERROR_CODE,
} from "@anwb/webshop-helpers";
import ProductImage from "@anwb/webshop-product-image";
import ProductPrice from "@anwb/webshop-product-price";
import { AnwbProductTypes } from "@anwb/webshop-types";
import { useMemo } from "react";

import { CartListProductDetails } from "../cart-list-product-details/CartListProductDetails";
import MakeSelectAmount from "../make-select-amount/MakeSelectAmount";
import { getLabelVariantFromType } from "./cartListRow.helper";
import {
	CartListFirstRowLine,
	CartListRowContainer,
	CartListRowLine,
	CartListRowProductImage,
	CartListRowProductText,
	ShoppingCartProductDetails,
} from "./styles/cart-list-row.styled";

type CardListRowProps = {
	handleQuantityChange: (id: string, quantity: number) => void;
	hidePrices?: boolean;
	item: CartItem;
};

const MAX_QUANTITY_TICKETS = 30;

function CartRowComponent({
	handleQuantityChange,
	hidePrices = false,
	item,
}: CardListRowProps) {
	const anwbProductType = item.anwbProductType?.sub;
	const isAvailableInStores =
		!item.isDropshipment &&
		!item.isMiraklProduct &&
		anwbProductType !== AnwbProductTypes.VIGNET;
	const isTicketType = anwbProductType === AnwbProductTypes.TICKET;

	const deliveryTimeText = (() => {
		if (!item.shippingTime || item.shippingTime.length === 0) {
			return "Levertijd: 1-3 werkdagen";
		}

		if (isAvailableInStores) {
			return `Levertijd: ${item.shippingTime}.`;
		}

		return `Levertijd: ${item.shippingTime} en is niet af te halen in de ANWB winkel.`;
	})();

	// // The default is 10, since Magento returns null when the stock is over threshold (10)
	const availableQuantity = Math.min(item.onlyXLeftInStock ?? 10, 10);

	// Todo: Is this still being implemented correctly?
	const cartItemStockErrorMessages = getCartItemErrorMessages(item.messages, [
		OUT_OF_STOCK_ERROR_CODE,
		INSUFFICIENT_STOCK_ERROR_CODE,
		QUANTITY_AVAILABLE_ERROR_CODE,
	]);

	const itemDetails = useMemo(() => {
		const list: Array<{
			optionLabel: string;
			valueLabel: number | React.JSX.Element | string;
		}> = [
			{
				optionLabel: "Aantal",
				valueLabel: (
					<MakeSelectAmount
						errorMessage={cartItemStockErrorMessages[0]?.message}
						handleQuantityChange={handleQuantityChange}
						id={item.id}
						label={"Aantal"}
						maximumQuantity={
							// TODO: This is a temporary solution (ANWB-3487) which allows users to select up to 30 tickets in the dropdown,
							// ignoring the actual available quantity in stock. Follow up story created https://emico-commerce.atlassian.net/browse/ANWB-3607
							isTicketType ? MAX_QUANTITY_TICKETS : availableQuantity
						}
						quantity={item.quantity}
					/>
				),
			},
		];

		if (isTicketType) {
			const offerDate =
				item.marketplaceOffer?.priceAdditionalInfo?.find(
					(obj) => obj.key === "date",
				)?.value ||
				// @ts-expect-error this data is for order - not needed anymore after thank you page refactor
				item?.offerData?.ticketDate;
			if (offerDate) {
				list.push({
					optionLabel: "Datum",
					valueLabel: formatDateToDutch(offerDate),
				});
			}
		}

		if (anwbProductType === AnwbProductTypes.VIGNET) {
			item.vignetOptions?.forEach(({ label, value }) =>
				list.push({ optionLabel: label, valueLabel: value }),
			);
		}

		if (item?.customizableOptions) {
			item.customizableOptions.forEach(({ label, values }) =>
				list.push({
					optionLabel: label,
					valueLabel: values.find((item) => item?.value)?.value ?? "",
				}),
			);
		}

		return [...list, ...(item.configurableOptions ?? [])];
	}, [
		anwbProductType,
		availableQuantity,
		cartItemStockErrorMessages,
		handleQuantityChange,
		item.configurableOptions,
		item.customizableOptions,
		item.id,
		item.marketplaceOffer?.priceAdditionalInfo,
		// @ts-expect-error this data is for order - not needed anymore after thank you page refactor
		item?.offerData?.ticketDate,
		item.quantity,
		item.vignetOptions,
	]);

	const price = useMemo(
		() => ({
			defaultPrice: item.marketplaceOffer
				? (item.marketplaceOffer.finalPrice ?? undefined)
				: (item.customerGroupPrices?.find(
						({ customerGroupId }) => customerGroupId === 0,
					)?.price ?? undefined),
			memberFromPrice: item.marketplaceOffer
				? undefined
				: (item.memberFromPrice ?? undefined),
			membersPrice: item.marketplaceOffer
				? undefined
				: (item.customerGroupPrices?.find(
						({ customerGroupId }) => customerGroupId === 1,
					)?.price ?? undefined),
			nonmemberFromPrice: item.marketplaceOffer
				? (item.marketplaceOffer?.originalPrice ?? undefined)
				: (item.nonmemberFromPrice ?? undefined),
		}),
		[item.customerGroupPrices, item.memberFromPrice, item.nonmemberFromPrice],
	);

	return (
		<CartListRowContainer>
			<CartListRowLine>
				<CartListRowProductImage>
					<a aria-label={item.name} href={item.targetUrl}>
						<ProductImage
							image={{ alt: item.image?.altText, url: item.image?.url || null }}
							labels={item.labels?.map((label) => ({
								text: label.label,
								variant: getLabelVariantFromType(label.type),
							}))}
							variant="small"
						/>
					</a>

					<CartListFirstRowLine type="mobile">
						<CartListRowProductText
							aria-label={item.name}
							href={item.targetUrl}
							tagName="a"
							variant="component-title"
						>
							{item.name}
						</CartListRowProductText>
						<Typography variant="support-text">{deliveryTimeText}</Typography>
					</CartListFirstRowLine>
				</CartListRowProductImage>

				<ShoppingCartProductDetails>
					<CartListFirstRowLine type="desktop">
						<Typography as="h2">
							<CartListRowProductText
								aria-label={item.name}
								href={item.targetUrl}
								tagName="a"
								variant="component-title"
							>
								{item.name}
							</CartListRowProductText>
						</Typography>
						<Typography style={{ textAlign: "right" }} variant="support-text">
							{deliveryTimeText}
						</Typography>
					</CartListFirstRowLine>

					<Grid>
						<Grid.Item>
							<div>
								<CartListProductDetails items={itemDetails} />
							</div>
						</Grid.Item>

						<Grid.Aside>
							{!hidePrices && item.sku && <ProductPrice price={price} />}
						</Grid.Aside>
					</Grid>
				</ShoppingCartProductDetails>
			</CartListRowLine>
		</CartListRowContainer>
	);
}

export default CartRowComponent;

import type React from "react";

import Typography from "@anwb/poncho/components/typography";

import { CartListRowProductDetails } from "./styles/cart-list-product-details";

type CartListProductDetailsProps = {
	items?: Array<{
		optionLabel: string;
		valueLabel: number | React.JSX.Element | string;
	}>;
};

export function CartListProductDetails({ items }: CartListProductDetailsProps) {
	return (
		<>
			{items?.map(({ optionLabel, valueLabel }, index) => (
				<CartListRowProductDetails key={`${optionLabel}-${index}`}>
					<span>
						<Typography as="p" variant="label-title">
							{optionLabel}
						</Typography>
					</span>
					<span>
						<Typography variant="body-text">{valueLabel}</Typography>
					</span>
				</CartListRowProductDetails>
			))}
		</>
	);
}

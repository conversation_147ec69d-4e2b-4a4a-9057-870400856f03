{"name": "@anwb/webshop-component-navigation-tiles", "description": "The navigation tiles component used by the webshop applications and packages", "version": "0.0.1", "private": true, "type": "module", "main": "dist/index.js", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc --project tsconfig.build.json", "dev": "tsc --project tsconfig.build.json --watch", "lint": "eslint --cache .", "typecheck": "tsc --noEmit"}, "dependencies": {"@anwb/poncho": "4.64.3", "@anwb/webshop-helpers": "workspace:*", "@anwb/webshop-types": "workspace:*", "@prismicio/client": "^7.17.3"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11"}, "devDependencies": {"@anwb/tools-eslint-config": "workspace:*", "@total-typescript/tsconfig": "^1.0.4", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "eslint": "^9.24.0", "react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11", "typescript": "^5.8.3"}}
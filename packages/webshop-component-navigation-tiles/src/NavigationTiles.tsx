import type {
	ContentPageDocumentDataBodyNavigationTilesSliceItem as NavigationTilesItem,
	ContentPageDocumentDataBodyNavigationTilesSlicePrimary as NavigationTilesSlicePrimary,
} from "@anwb/webshop-prismic";

import HorizontalScroll from "@anwb/poncho/components/horizontal-scroll";
import Typography from "@anwb/poncho/components/typography";
import { generateImageURL } from "@anwb/webshop-helpers";
import { asText, isFilled } from "@prismicio/client";

import { ImgWrapper } from "./components/Image/styles/image.styled";
import {
	NavigationTilesContainer,
	TileScrollWrapper,
} from "./components/navigation-tiles-application/styles/navigation-tiles-application.styled";
import Tile from "./components/Tile/Tile";
import { TitleWrapper } from "./components/Title/styles/title.styled";

function NavigationTiles({
	items,
	primary,
}: {
	items: Array<NavigationTilesItem>;
	primary: NavigationTilesSlicePrimary;
}) {
	if (!items.length) return null;

	const { hide_title, title } = primary;

	return (
		<NavigationTilesContainer>
			{!hide_title && isFilled.richText(title) && (
				<Typography variant="group-component-title">{asText(title)}</Typography>
			)}

			<TileScrollWrapper>
				<HorizontalScroll ignoreVerticalScroll slidesPerView="auto">
					{items.map((tile) => (
						<HorizontalScroll.Item key={asText(tile.title)}>
							<Tile link={tile.link}>
								{isFilled.richText(tile.title) && (
									<TitleWrapper>
										<Typography tagName="h3" variant="component-subtitle">
											{asText(tile.title)}
										</Typography>
									</TitleWrapper>
								)}

								{isFilled.richText(tile.image_source) && (
									<ImgWrapper
										$imgSrc={generateImageURL({
											aspectRatio: "1:1",
											format: "webp",
											gravity: "center",
											height: 130,
											url: asText(tile.image_source),
											width: 130,
										})}
									/>
								)}
							</Tile>
						</HorizontalScroll.Item>
					))}
				</HorizontalScroll>
			</TileScrollWrapper>
		</NavigationTilesContainer>
	);
}

export default NavigationTiles;

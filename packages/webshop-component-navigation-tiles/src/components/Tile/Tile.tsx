import type { ContentPageDocumentDataBodyNavigationTilesSliceItem as NavigationTilesItem } from "@anwb/webshop-prismic";
import type { PropsWithChildren } from "react";

import { asText } from "@prismicio/client";

import { StyledTile } from "./styles/tile.styled";

type Props = PropsWithChildren<{
	link: NavigationTilesItem["link"];
}>;

export function Tile({ children, link }: Props) {
	return (
		<StyledTile href={asText(link)} target="_self">
			{children}
		</StyledTile>
	);
}

export default Tile;

type FormStorageTimers = Record<string, number>; // 'number' is the return value of setTimeout

const storageTimers: FormStorageTimers = {};

export const setFormStorage = (storageKey: string, data: unknown) => {
	const previousTimer = storageTimers[storageKey];
	if (previousTimer != null) {
		// clean up the previous timer
		clearTimeout(storageTimers[storageKey]);
	}

	storageTimers[storageKey] = window.setTimeout(() => {
		sessionStorage.setItem(storageKey, JSON.stringify(data));
		// eslint-disable-next-line @typescript-eslint/no-dynamic-delete
		delete storageTimers[storageKey];
	}, 1000);
};

import type { NotificationError as Props } from "../types";

export const getErrorMessage = ({ context, message }: Props) => {
	switch (context) {
		case "general":
			return "Er is een technisch probleem waardoor het op dit moment niet mogelijk is om een afspraak te maken. Probeer het later nog eens.";
		case "get-address":
		case "get-basic-data":
			return "We konden je persoonlijke gegevens niet ophalen. Probeer het later nog eens.";
		case "get-claim":
			return "We konden je schadeclaim niet ophalen. Probeer het later nog eens.";
		case "get-dates":
			// possible errors:
			// with invalid settlementId --> No query results for model [App\Domains\Base\Models\Establishment] <settlementId>
			if (message?.includes("query results")) {
				return "Geen beschikbare data gevonden voor de gekozen schadehersteller. Kies een andere schadehersteller of probeer het later opnieuw.";
			}
			break;
		case "get-repairers":
			// possible errors:
			// with invalid cityOrPostalCode --> failed to retrieve location for <cityOrPostalCode>
			if (message?.includes("retrieve location")) {
				return "Geen schadeherstellers gevonden voor plaats of postcode. Kies een andere plaats of postcode of probeer het later opnieuw.";
			}
			break;
		case "get-times":
			// possible errors:
			// with invalid settlementId --> No query results for model [App\Domains\Base\Models\Establishment] <settlementId>
			// with invalid date --> No availability found on specified day

			if (message?.includes("query results")) {
				return "Geen beschikbare tijden gevonden voor de gekozen schadehersteller. Kies een andere schadehersteller of probeer het later opnieuw.";
			}
			if (message?.includes("specified day")) {
				return "Geen beschikbare tijden gevonden voor de gekozen dag. Kies een andere dag of probeer het later opnieuw.";
			}
			break;
		case "post-appointment":
			return "We konden je afspraak niet aanmaken door een technisch probleem. Probeer het later nog eens.";
		default:
			return "Er is een technisch probleem. Probeer het later nog eens.";
	}
};

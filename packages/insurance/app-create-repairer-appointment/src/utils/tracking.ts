import { trimPostalCodeOrCity } from "@anwb/insurance-service-support/utils";
import { trackEvent } from "@anwb/poncho/utilities/tracking";

import { TRACKING_CATEGORY, TRACKING_VARIANT } from "../constants";

type Step = "S1" | "S2" | "S3";
export type StepName = "Controleer" | "Maak afspraak" | "Zoek";

type TrackDialogStartFinishProps = {
	product: string;
	variant: string;
} & (
	| {
			action: string;
			actionValue: string;
			field: string;
			status: "finish";
	  }
	| {
			action?: never;
			actionValue?: never;
			field?: never;
			status: "start";
	  }
);

export const trackDialogStartFinish = ({
	action = "",
	actionValue = "",
	field = "",
	product,
	status,
	variant,
}: TrackDialogStartFinishProps) => {
	trackEvent({
		action,
		actionValue: actionValue ? `vervangend vervoer ${actionValue}` : "",
		category: TRACKING_CATEGORY,
		component: "dialoog",
		event: "user_interaction",
		field,
		product,
		status,
		type: "funnel",
		variant: variant ? `${TRACKING_VARIANT} - ${variant}` : TRACKING_VARIANT,
	});
};

type TrackFunnelStartFinishProps = {
	component: StepName;
	field: Step;
	product: string;
	status: "finish" | "start";
	variant: string;
};

export const trackFunnelStartFinish = ({
	component,
	field,
	product,
	status,
	variant,
}: TrackFunnelStartFinishProps) => {
	trackEvent({
		category: TRACKING_CATEGORY,
		component,
		event: "user_interaction",
		field,
		product,
		status,
		type: "funnel",
		variant: variant ? `${TRACKING_VARIANT} - ${variant}` : TRACKING_VARIANT,
	});
};

type TrackSearchProps = {
	damageType: string;
	searchTerm: string;
	vehicleType: string;
};

export const trackSearch = ({
	damageType,
	searchTerm,
	vehicleType,
}: TrackSearchProps) => {
	trackEvent({
		action: "search",
		event: "search_and_filter",
		filterValue: `voertuig, ${vehicleType} | schade, ${damageType}`,
		searchTerm: trimPostalCodeOrCity(searchTerm),
	});
};

type TrackSearchResultsProps = {
	damageType: string;
	searchTerm: string;
	totalResults: string;
	vehicleType: string;
};

export const trackSearchResults = ({
	damageType,
	searchTerm,
	totalResults,
	vehicleType,
}: TrackSearchResultsProps) => {
	trackEvent({
		event: "view_search_results",
		filterValue: `voertuig, ${vehicleType} | schade, ${damageType}`,
		searchTerm: trimPostalCodeOrCity(searchTerm),
		totalResults,
	});
};

type TrackNotificationProps = {
	component: StepName;
	status: "alert" | "error" | "info" | "success";
	text: string;
};

export const trackNotification = ({
	component,
	status,
	text,
}: TrackNotificationProps) => {
	trackEvent({
		category: TRACKING_CATEGORY,
		component,
		event: "visibility",
		status,
		text,
		type: "notification",
		variant: TRACKING_VARIANT,
	});
};

type TrackValidFormInputProps = {
	action: "changed" | "selected" | "submitted" | "unselected";
	actionValue: string;
	component: StepName;
	field: string;
	product: string;
};

export const trackValidFormInput = ({
	action,
	actionValue,
	component,
	field,
	product,
}: TrackValidFormInputProps) => {
	trackEvent({
		action,
		actionValue,
		category: TRACKING_CATEGORY,
		component,
		event: "user_interaction",
		field,
		product,
		status: "valid",
		type: "form",
		variant: TRACKING_VARIANT,
	});
};

type TrackInvalidFormInputProps = {
	action: "changed" | "selected" | "submitted" | "unselected";
	actionValue: string;
	component: StepName;
	field: string;
	product: string;
};

export const trackInvalidFormInput = ({
	action,
	actionValue,
	component,
	field,
	product,
}: TrackInvalidFormInputProps) => {
	trackEvent({
		action,
		actionValue,
		category: TRACKING_CATEGORY,
		component,
		event: "user_interaction",
		field,
		product,
		status: "invalid",
		type: "form",
		variant: TRACKING_VARIANT,
	});
};

type TrackModifyLinkClickProps = {
	actionValue: string;
	component: StepName;
	product: string;
};

export const trackModifyLinkClick = ({
	actionValue,
	component,
	product,
}: TrackModifyLinkClickProps) => {
	trackEvent({
		action: "click",
		actionValue,
		category: TRACKING_CATEGORY,
		component,
		event: "user_interaction",
		product,
		type: "form",
		variant: TRACKING_VARIANT,
	});
};

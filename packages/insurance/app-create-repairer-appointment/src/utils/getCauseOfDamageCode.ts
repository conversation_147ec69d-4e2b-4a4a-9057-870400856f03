const DAMAGE_CAUSES = {
	// 100: 'verkeer',// onbekend in pulse
	111: "voertuig", // aan<PERSON>j<PERSON> met een voertuig
	112: "persoon", // aanrij<PERSON> met een persoon
	113: "wegmeubilair", // aanrijding wegmeubilair
	114: "object", // aanrijding met ander vast object
	116: "dieren", // aanrijding met loslopende/vliegende dieren
	// 117: 'slippen', // onbekend in pulse
	// 118: 'omslaan', // onbekend in pulse
	210: "brand", // brand
	311: "diefstal", // diefstal
	// 390: 'inbraak', // onbekend in pulse
	410: "hagel", // hagel
	420: "storm", // storm
	600: "vandalisme", // vandalisme
	999: "anders", // anders
} as Record<string, string>;

export function getCauseOfDamageCode(causeOfDamage?: string) {
	if (!causeOfDamage) return null;
	let causeOfDamageCode = "999";

	Object.keys(DAMAGE_CAUSES).forEach((key) => {
		// regex matches " " and "_" characters
		const causeOfDamageStrings = causeOfDamage.split(/[ _]/);

		const matchingCode = causeOfDamageStrings.some((cause) =>
			cause.toLowerCase().includes(key),
		);

		if (matchingCode) {
			causeOfDamageCode = key;
		}
	});

	return causeOfDamageCode;
}

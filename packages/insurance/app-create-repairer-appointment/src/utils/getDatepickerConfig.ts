import type { Dates } from "@anwb/insurance-service-support/types";

export const getDatepickerConfig = (dates?: Dates) => {
	if (!dates) {
		return {
			disabledDays: [],
			max: undefined,
			min: undefined,
		};
	}

	const datesAsISOString = Object.keys(dates);

	const min = datesAsISOString.find((ISOdate) => {
		return dates[ISOdate];
	});

	const max = datesAsISOString.reverse().find((ISOdate) => {
		return dates[ISOdate];
	});

	const disabledDays = datesAsISOString.reduce(
		(accumulator: Array<Date>, date) => {
			const enabled = dates[date];
			if (!enabled) {
				accumulator.push(new Date(date));
			}
			return accumulator;
		},
		[],
	);

	return { disabledDays, max, min };
};

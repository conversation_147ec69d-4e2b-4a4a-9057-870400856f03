import { trackEvent } from "@anwb/poncho/utilities/tracking";

import type { CategoryName } from "../../types";

type Step = "S1" | "S2" | "S3";
type StepName = "Hoe nu verder" | "Kies je verzekering" | "Wat is er gebeurd";

type TrackFunnelProps = {
	action?: "selected";
	actionValue?: string | undefined;
	component: StepName;
	field: Step;
	status: "finish" | "start";
};

const defaultValues = {
	category: "Verzekeringen - Schade",
	variant: "Schadehulp",
};

export const trackFunnel = ({
	action,
	actionValue,
	component,
	field,
	status,
}: TrackFunnelProps) => {
	trackEvent({
		action,
		actionValue,
		component,
		event: "user_interaction",
		field,
		product: "",
		status,
		type: "funnel",
		...defaultValues,
	});
};

export const trackPolicyCategory = (actionValue: CategoryName) => {
	trackEvent({
		action: "click",
		actionValue,
		component: "Kies je verzekering",
		event: "user_interaction",
		field: "S1",
		product: "",
		status: "",
		type: "form",
		...defaultValues,
	});
};

export const trackButtonVisibility = (text: string) => {
	trackEvent({
		component: "Hoe nu verder",
		event: "visibility",
		position: "",
		product: "",
		status: "",
		text,
		type: "button",
		...defaultValues,
	});
};

import type { Category, Product } from "../types";

export const categories: Array<Category> = [
	{ id: "transport", title: "Op de weg" },
	{ id: "leisure", title: "Vakantie en vrije tijd" },
	{ id: "personal", title: "Thuis en persoonlijk" },
];

export const products: Array<Product> = [
	{
		category: "transport",
		damageCause: [
			"carCollision",
			"carNature",
			"carFire",
			"carVandalism",
			"carTheft",
			"carWindow",
		],
		icon: "car-label",
		id: "car",
		title: "Auto",
	},
	{
		category: "transport",
		damageCause: ["bicycleTheft", "bicycleTheftOfAccessories", "bicycleDamage"],
		icon: "bike",
		id: "bike",
		title: "Fiets",
	},
	{
		category: "transport",
		damageCause: [
			"mopedCollision",
			"mopedNature",
			"mopedFire",
			"mopedVandalism",
			"mopedTheft",
		],
		icon: "scooter",
		id: "moped",
		title: "Bromfiets / Scooter",
	},
	{
		category: "transport",
		damageCause: [
			"motorCollision",
			"motorNature",
			"motorFire",
			"motorVandalism",
			"motorTheft",
		],
		icon: "motor-fast",
		id: "motor",
		title: "Motor",
	},
	{
		category: "leisure",
		damageCause: [
			"travelSicknessAtHome",
			"travelSicknessAtStay",
			"travelLostGoods",
			"travelHomeEarly",
			"travelDelay",
			"travelCancelled",
		],
		icon: "suitcase",
		id: "travel",
		title: "Reis en/of annulering",
	},
	{
		category: "transport",
		damageCause: [
			"caravanCollision",
			"caravanNature",
			"caravanFire",
			"caravanTheft",
			"caravanVandalism",
		],
		icon: "caravan-folding",
		id: "caravan",
		title: "Caravan",
	},
	{
		category: "transport",
		damageCause: [
			"camperCollision",
			"camperNature",
			"camperFire",
			"camperVandalism",
			"camperTheft",
			"camperWindow",
		],
		icon: "camper",
		id: "camper",
		title: "Camper",
	},
	{
		category: "leisure",
		damageCause: ["boatCollision", "boatTheft"],
		icon: "boat-sailing",
		id: "boat",
		title: "Boot",
	},
	{
		category: "personal",
		icon: "inboedel",
		id: "householdEffect",
		title: "Inboedel",
	},
	{
		category: "personal",
		damageCause: [
			"buildingFire",
			"buildingGlass",
			"buildingTheft",
			"buildingNature",
		],
		icon: "house",
		id: "building",
		title: "Opstal",
	},
	{
		category: "personal",
		icon: "aansprakelijkheid",
		id: "liability",
		title: "Aansprakelijkheid",
	},
	{
		category: "personal",
		damageCause: ["legalCounselTraffic", "legalCounselOther"],
		icon: "legal-advice",
		id: "legalCounsel",
		title: "Rechtsbijstand",
	},
	{
		category: "personal",
		icon: "man-injured",
		id: "accidents",
		title: "Ongevallen",
	},
];

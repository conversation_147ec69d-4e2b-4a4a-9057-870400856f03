import type { DamageCause, DamageCauseMap } from "../types";

const collision = { title: "Aan<PERSON>jding" };
const nature = { title: "Storm, hagel of wateroverlast" };
const vandalism = { title: "Vandalisme" };
const theft = { title: "Die<PERSON><PERSON>" };
const fire = { title: "Brand" };
const window = { title: "Ruitschade" };

export const damageCause: Record<keyof DamageCauseMap, DamageCause> = {
	bicycleDamage: {
		title: "Beschadiging",
	},
	bicycleTheft: {
		title: "Diefs<PERSON> van de fiets",
	},
	bicycleTheftOfAccessories: {
		title: "Die<PERSON><PERSON> van onderdelen",
	},
	boatCollision: {
		title: "Aanvaring",
	},
	boatTheft: {
		title: "Inbraak en diefstal",
	},
	buildingFire: {
		title: "Brand / ontploffing",
	},
	buildingGlass: {
		title: "Ruitschade",
	},
	buildingNature: {
		...nature,
	},
	buildingTheft: {
		title: "Inbraak en diefstal",
	},
	camperCollision: {
		...collision,
	},
	camperFire: {
		...fire,
	},
	camperNature: {
		...nature,
	},
	camperTheft: {
		...theft,
	},
	camperVandalism: {
		...vandalism,
	},
	camperWindow: {
		...window,
	},
	caravanCollision: {
		...collision,
	},
	caravanFire: {
		...fire,
	},
	caravanNature: {
		...nature,
	},
	caravanTheft: {
		...theft,
	},
	caravanVandalism: {
		...vandalism,
	},
	carCollision: {
		...collision,
	},
	carFire: {
		...fire,
	},
	carNature: {
		...nature,
	},
	carTheft: {
		...theft,
	},
	carVandalism: {
		...vandalism,
	},
	carWindow: {
		...window,
	},
	legalCounselOther: {
		title: "Overige",
	},
	legalCounselTraffic: {
		title: "Verkeer",
	},
	mopedCollision: {
		...collision,
	},
	mopedFire: {
		...fire,
	},
	mopedNature: {
		...nature,
	},
	mopedTheft: {
		...theft,
	},
	mopedVandalism: {
		...vandalism,
	},
	motorCollision: {
		...collision,
	},
	motorFire: {
		...fire,
	},
	motorNature: {
		...nature,
	},
	motorTheft: {
		...theft,
	},
	motorVandalism: {
		...vandalism,
	},
	travelCancelled: {
		title: "Niet op reis gegaan",
	},
	travelDelay: {
		title: "Vertraging tijdens de reis",
	},
	travelHomeEarly: {
		title: "Eerder naar huis",
	},
	travelLostGoods: {
		title: "Spullen verloren of beschadigd",
	},
	travelSicknessAtHome: {
		title: "Ziekte of ongeval thuis",
	},
	travelSicknessAtStay: {
		title: "Ziekte of ongeval op vakantie",
	},
};

export default damageCause;

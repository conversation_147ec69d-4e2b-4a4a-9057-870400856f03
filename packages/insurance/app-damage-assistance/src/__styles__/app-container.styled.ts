import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import { PanelContainer } from "@anwb/poncho/components/panel";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

const appContainerStyles = ({ theme }: DefaultThemeProps) => css`
	${PanelContainer} {
		margin-left: -${pxToRem(theme.spacing["200"])};
		margin-right: -${pxToRem(theme.spacing["200"])};
	}
`;

const StyledAppContainer = styled.div`
	${appContainerStyles};
`;

export default StyledAppContainer;

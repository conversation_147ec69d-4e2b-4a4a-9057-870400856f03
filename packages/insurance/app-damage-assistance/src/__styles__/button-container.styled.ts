import type { DefaultThemeProps } from "@anwb/poncho/design-tokens/theme";

import { ButtonContainer } from "@anwb/poncho/components/button";
import { pxToRem } from "@anwb/poncho/design-tokens/style-utilities";
import styled, { css } from "styled-components";

const buttonContainer = ({ theme }: DefaultThemeProps) => css`
	${ButtonContainer} {
		margin: 0 ${pxToRem(theme.spacing["300"])} ${pxToRem(theme.spacing["300"])}
			0 !important;

		&.isActive {
			border-color: ${theme.colors.buttons.borderButtonTertiaryOnDarkActive};
		}
	}
`;

const StyedButtonContainer = styled.div`
	${buttonContainer};
`;

export default StyedButtonContainer;

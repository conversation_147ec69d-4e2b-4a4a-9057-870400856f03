import Typography from "@anwb/poncho/components/typography";

import type { AdviseContent } from "../../types";

import Paragraph from "../../__styles__/paragraph.styled";
import { ButtonWithTracking } from "../buttons/button-with-tracking";

type Props = {
	adviseContent: Record<string, AdviseContent>;
	adviseTitles: Record<string, string>;
};

export function Advise({ adviseContent, adviseTitles }: Props) {
	const paragraphsNumbers = Object.keys(adviseContent) as unknown as Array<
		keyof Props["adviseContent"]
	>;

	const paragraphs = paragraphsNumbers.map((paragraph) => {
		const title = adviseTitles[paragraph];
		const content = {
			button: adviseContent[paragraph]?.button,
			text: adviseContent[paragraph]?.text,
		};

		return (
			<Paragraph key={paragraph}>
				{title && (
					<Typography tagName="h3" variant="content-subtitle">
						{title}
					</Typography>
				)}

				{content.text && (
					<Typography variant="body-text">{content.text}</Typography>
				)}

				{content.button && <ButtonWithTracking button={content.button} />}
			</Paragraph>
		);
	});

	return <>{paragraphs}</>;
}

export default Advise;

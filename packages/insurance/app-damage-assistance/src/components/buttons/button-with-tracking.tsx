import { Button } from "@anwb/poncho/components/button";
import { useEffect } from "react";

import { trackButtonVisibility } from "../../utils/tracking";

type Props = {
	button: {
		href: string;
		text: string;
	};
};

export function ButtonWithTracking({ button }: Props) {
	useEffect(() => {
		trackButtonVisibility(button.text);
	});

	return (
		<Button color="secondary" href={button.href}>
			{button.text}
		</Button>
	);
}

export default ButtonWithTracking;

import { useInsuranceAccountTracking } from "@anwb/insurance-service-support/hooks";
import ButtonSmall from "@anwb/poncho/components/button";
import FormOptionCard from "@anwb/poncho/components/form-option-card";
import Panel from "@anwb/poncho/components/panel";
import Typography from "@anwb/poncho/components/typography";
import { useEffect, useState } from "react";

import type {
	AdviseContent,
	CategoryId,
	DamageCause,
	DamageCauseMap,
	Product,
} from "./types";

import StyledAppContainer from "./__styles__/app-container.styled";
import StyedButtonContainer from "./__styles__/button-container.styled";
import { Advise } from "./components/advise";
import { damageCause } from "./data/damage-cause";
import { categories, products } from "./data/products";
import { trackFunnel, trackPolicyCategory } from "./utils/tracking";

type State = {
	advise: keyof DamageCauseMap | undefined;
	step1: Product | undefined;
	step2: DamageCause | undefined;
};

type Props = {
	adviseContent: {
		[key in keyof DamageCauseMap]: Record<string, AdviseContent>;
	};
	adviseTitles: Record<string, string>;
};

function AppDamageAssistance({ adviseContent, adviseTitles }: Props) {
	useInsuranceAccountTracking();

	const [category, setCategory] = useState<CategoryId>("transport");
	const [curentStep, setCurrentStep] = useState(1);
	const [chosenOptions, setChosenOptions] = useState<State>({
		advise: undefined,
		step1: undefined,
		step2: undefined,
	});

	useEffect(() => {
		trackFunnel({
			component: "Kies je verzekering",
			field: "S1",
			status: "start",
		});
	}, []);

	const shouldShowStep2 =
		chosenOptions.step1 === undefined || chosenOptions.step1.damageCause;

	return (
		<StyledAppContainer>
			<Panel variant="informative">
				<Panel.Header title="Kies je verzekering">
					{chosenOptions.step1 && (
						<Typography variant="component-subtitle">
							Gekozen: {chosenOptions.step1.title}{" "}
							<Typography
								onClick={() => {
									setCurrentStep(1);
									setChosenOptions({
										advise: undefined,
										step1: undefined,
										step2: undefined,
									});
									trackFunnel({
										actionValue: "wijzig",
										component: "Kies je verzekering",
										field: "S1",
										status: "start",
									});
								}}
								tagName="span"
								variant="link-text"
							>
								(Wijzig)
							</Typography>
						</Typography>
					)}
				</Panel.Header>
				{curentStep === 1 && (
					<Panel.Content>
						<StyedButtonContainer>
							{categories.map((cat) => (
								<ButtonSmall
									className={cat.id === category ? "isActive" : undefined}
									color="tertiary"
									key={cat.id}
									onClick={() => {
										setCategory(cat.id);
										trackPolicyCategory(cat.title);
									}}
								>
									{cat.title}
								</ButtonSmall>
							))}
						</StyedButtonContainer>

						<FormOptionCard
							columns={{ lg: 2, md: 1, sm: 1 }}
							inputType="radio"
							name="form-option-card-product"
							onChange={(id) => {
								const chosenProduct = products.find(
									(product) => product.id === id,
								);

								trackFunnel({
									action: "selected",
									actionValue: chosenProduct?.title,
									component: "Kies je verzekering",
									field: "S1",
									status: "finish",
								});

								if (chosenProduct?.damageCause) {
									setChosenOptions({
										advise: undefined,
										step1: chosenProduct,
										step2: undefined,
									});
									setCurrentStep(2);
									trackFunnel({
										component: "Kies je verzekering",
										field: "S2",
										status: "start",
									});
								} else {
									setChosenOptions({
										advise: id as keyof DamageCauseMap,
										step1: chosenProduct,
										step2: undefined,
									});
									setCurrentStep(3);
									trackFunnel({
										component: "Kies je verzekering",
										field: "S3",
										status: "start",
									});
								}
							}}
							required
						>
							{products
								.filter((product) => product.category === category)
								.map((product) => (
									<FormOptionCard.Item
										icon={{
											dataTest: "panel-option",
											size: "md",
											type: "illustrative",
											variant: product.icon,
										}}
										key={product.id}
										label={product.title}
										value={product.id.toString()}
									/>
								))}
						</FormOptionCard>
					</Panel.Content>
				)}
			</Panel>

			{shouldShowStep2 && (
				<Panel variant="informative">
					<Panel.Header title="Wat is er gebeurd?">
						{chosenOptions.step2 && (
							<Typography variant="component-subtitle">
								Gekozen: {chosenOptions.step2.title}{" "}
								<Typography
									onClick={() => {
										setChosenOptions((state) => {
											const newState = {
												...state,
												step2: undefined,
											};
											return newState;
										});
										setCurrentStep(2);
										trackFunnel({
											actionValue: "wijzig",
											component: "Wat is er gebeurd",
											field: "S2",
											status: "start",
										});
									}}
									tagName="span"
									variant="link-text"
								>
									(Wijzig)
								</Typography>
							</Typography>
						)}
					</Panel.Header>
					{curentStep === 2 && chosenOptions.step1?.damageCause && (
						<Panel.Content>
							<FormOptionCard
								columns={{ lg: 3, md: 2, sm: 1 }}
								inputType="radio"
								name="form-option-card-cause"
								onChange={(id) => {
									const cause = damageCause[id as keyof DamageCauseMap];

									setChosenOptions((state) => {
										const newState = {
											...state,
											advise: id as keyof DamageCauseMap,
											step2: cause,
										};
										return newState;
									});
									setCurrentStep(3);
									trackFunnel({
										action: "selected",
										actionValue: cause.title + (cause.tagline ?? ""),
										component: "Wat is er gebeurd",
										field: "S2",
										status: "finish",
									});
									trackFunnel({
										component: "Kies je verzekering",
										field: "S3",
										status: "start",
									});
								}}
								required
							>
								{chosenOptions.step1.damageCause.map((id) => {
									const cause = damageCause[id];

									return (
										<FormOptionCard.Item
											key={id}
											label={cause.title}
											supportText={cause.tagline}
											value={id}
										/>
									);
								})}
							</FormOptionCard>
						</Panel.Content>
					)}
				</Panel>
			)}

			<Panel variant="informative">
				<Panel.Header title="Hoe nu verder?" />
				{curentStep === 3 && chosenOptions.advise && (
					<Panel.Content>
						<Advise
							adviseContent={adviseContent[chosenOptions.advise]}
							adviseTitles={adviseTitles}
						/>
					</Panel.Content>
				)}
			</Panel>
		</StyledAppContainer>
	);
}

export default AppDamageAssistance;

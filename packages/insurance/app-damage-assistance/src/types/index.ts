import type { IconProps } from "@anwb/poncho/components/icon";

export type AdviseContent = {
	button?: {
		href: string;
		text: string;
	};
	text?: string;
};

const _damageCausesArray = [
	"bicycleDamage",
	"bicycleTheft",
	"bicycleTheftOfAccessories",
	"boatCollision",
	"boatTheft",
	"buildingFire",
	"buildingGlass",
	"buildingNature",
	"buildingTheft",
	"camperCollision",
	"camperFire",
	"camperNature",
	"camperTheft",
	"camperVandalism",
	"camperWindow",
	"caravanCollision",
	"caravanFire",
	"caravanNature",
	"caravanTheft",
	"caravanVandalism",
	"carCollision",
	"carFire",
	"carNature",
	"carTheft",
	"carVandalism",
	"carWindow",
	"legalCounselOther",
	"legalCounselTraffic",
	"mopedCollision",
	"mopedFire",
	"mopedNature",
	"mopedTheft",
	"mopedVandalism",
	"motorCollision",
	"motorFire",
	"motorNature",
	"motorTheft",
	"motorVandalism",
	"travelCancelled",
	"travelDelay",
	"travelHomeEarly",
	"travelLostGoods",
	"travelSicknessAtHome",
	"travelSicknessAtStay",
] as const;

const _adviseTypesArray = [
	..._damageCausesArray,
	"accidents",
	"householdEffect",
	"liability",
] as const;

export type AdviseTypes = typeof _adviseTypesArray;
export type DamageCauses = typeof _damageCausesArray;

export type DamageCauseMap = Record<
	DamageCauses[number],
	Record<string, unknown>
>;

export type DamageCause = {
	tagline?: string;
	title: string;
};

export type CategoryId = "leisure" | "personal" | "transport";
export type CategoryName =
	| "Op de weg"
	| "Thuis en persoonlijk"
	| "Vakantie en vrije tijd";

export type Category = { id: CategoryId; title: CategoryName };

export type Product = {
	category: CategoryId;
	damageCause?: Array<keyof DamageCauseMap>;
	icon: IconProps["variant"];
	id: string;
	title: string;
};

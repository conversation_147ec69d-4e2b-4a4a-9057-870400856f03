{"name": "@anwb/insurance-app-damage-assistance", "description": "The insurance app damage assistance package", "version": "2.2.5", "private": true, "type": "module", "main": "dist/index.js", "exports": {".": "./dist/index.js"}, "scripts": {"build": "tsc --project tsconfig.build.json", "dev": "tsc --project tsconfig.build.json --watch", "lint": "eslint --cache --report-unused-disable-directives .", "typecheck": "tsc --noEmit"}, "dependencies": {"@anwb/insurance-service-support": "workspace:*", "@anwb/poncho": "4.64.3"}, "devDependencies": {"@anwb/tools-eslint-config": "workspace:*", "@total-typescript/tsconfig": "^1.0.4", "@types/node": "^22.15.17", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "@types/styled-components": "^5.1.34", "eslint": "^9.24.0", "react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11", "typescript": "^5.8.3"}, "peerDependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "styled-components": "^5.3.11"}}
export type ApiResponse<TData> =
	| ErrorResponse
	| SuccessResponse<TData>
	| ValidationErrorResponse;

type SuccessResponse<TData> = {
	data: TData;
	error: null;
	isSuccessful: true;
	validationErrors: null;
};

type ErrorResponse = {
	data: null;
	error: string;
	isSuccessful: false;
	validationErrors: null;
};

type ValidationErrorResponse = {
	data: null;
	error: null;
	isSuccessful: false;
	validationErrors: Array<{
		code: string;
		expected: string;
		message: string;
		path: Array<string>;
		received: string;
		source: string;
	}>;
};

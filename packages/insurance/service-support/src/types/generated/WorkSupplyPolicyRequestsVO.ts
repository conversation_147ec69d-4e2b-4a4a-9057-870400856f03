import type { WorkSupplyPolicyRequestsForApplicationProcessGroupVO } from "./WorkSupplyPolicyRequestsForApplicationProcessGroupVO";
import type { WorkSupplyUrgencyVO } from "./WorkSupplyUrgencyVO";

export type WorkSupplyPolicyRequestsVO = {
	/**
	 * This list contains all application process groups that have open policy
	 * requests.
	 */
	applicationProcessGroupList?: Array<WorkSupplyPolicyRequestsForApplicationProcessGroupVO>;
	/**
	 * This field contains the external identifier of the filter that the logged
	 * one user has defined in his preferences. This filters which policy requests
	 * are shown.
	 */
	filterByLogonUser?: string;
	filterByLogonUserName?: string;
	includeLowerIntermediaries?: boolean;
	intermediaryExtId?: string;
	logonUserExtId?: string;
	numberOfPolicyRequestsEnteredByCustomer?: number;
	/**
	 * This field contains the number of policy requests that the logged on user
	 * is responsible for.
	 */
	numberOfPolicyRequestsForLogonUser?: WorkSupplyUrgencyVO;
	numberOfPolicyRequestsInProgress?: number;
	numberOfPolicyRequestsInProgressResponsible?: number;
	numberOfPolicyRequestsIntermediary?: number;
	numberOfPolicyRequestsSpecialCases?: number;
	numberOfPolicyRequestsToAcceptAndAssignedTo?: number;
	numberOfPolicyRequestsWaitForSpecialistAssessment?: number;
	policyRequestProcessGroupExtId?: string;
	/**
	 * This field contains the external identifier of the status of the policy
	 * request.
	 */
	policyRequestStatusExtId?: string;
	priorityExtId?: string;
};

import type { WorkSupplyIncomingCommunicationsForInboxVO } from "./WorkSupplyIncomingCommunicationsForInboxVO";
import type { WorkSupplyUrgencyVO } from "./WorkSupplyUrgencyVO";

export type WorkSupplyIncomingCommunicationsVO = {
	communications?: WorkSupplyUrgencyVO;
	/**
	 * This list contains inboxes that potentially can have unlinked incoming
	 * communications.
	 */
	inboxList?: Array<WorkSupplyIncomingCommunicationsForInboxVO>;
	numberOfIncomingCommunicationsAssignedToUser?: number;
	numberOfIncomingCommunicationsNotAssigned?: number;
};

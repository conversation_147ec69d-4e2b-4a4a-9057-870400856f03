import type { WorkSupplyCommunicationsDetailsVO } from "./WorkSupplyCommunicationsDetailsVO";

export type WorkSupplyCommunicationsVO = {
	communicationsInInbox?: WorkSupplyCommunicationsDetailsVO;
	intermediaryDetails?: WorkSupplyCommunicationsDetailsVO;
	/** This field contains the open communications of the responsible user. */
	logonUserDetails?: WorkSupplyCommunicationsDetailsVO;
	/**
	 * This list contains the application processing groups the responsible user
	 * belongs to.
	 */
	processGroupList?: Array<WorkSupplyCommunicationsDetailsVO>;
};

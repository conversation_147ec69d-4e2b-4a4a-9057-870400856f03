import type { UpdateClaimVOClaimClaimIndicationVO } from "./UpdateClaimVOClaimClaimIndicationVO";
import type { UpdateClaimVOClaimCoverFlexibleFieldVO } from "./UpdateClaimVOClaimCoverFlexibleFieldVO";
import type { UpdateClaimVOClaimLocationVO } from "./UpdateClaimVOClaimLocationVO";
import type { UpdateClaimVOClaimPolicyVersionFlexibleFieldVO } from "./UpdateClaimVOClaimPolicyVersionFlexibleFieldVO";

export type UpdateClaimVO = {
	/** Indicates whether the are physical documents available for the claim. */
	arePhysicalDocumentsAvailable?: boolean;
	/** Indicates whether the 'Add details' dialogue can be used for this claim. */
	canAddDetailsDialogBeUsed?: boolean;
	claimClaimIndication?: UpdateClaimVOClaimClaimIndicationVO;
	claimCoverFlexibleFields?: Array<UpdateClaimVOClaimCoverFlexibleFieldVO>;
	/**
	 * The identification of the claim.
	 *
	 * @maxLength 255
	 */
	claimIdentificationMark?: string;
	/**
	 * The claim number of the claim as known in an external system.
	 *
	 * @maxLength 80
	 */
	claimIdentifier?: string;
	claimLocation?: UpdateClaimVOClaimLocationVO;
	claimPolicyVersionFlexibleFields?: Array<UpdateClaimVOClaimPolicyVersionFlexibleFieldVO>;
	/**
	 * The description of the claim.
	 *
	 * @maxLength 500
	 */
	description?: string;
	/** The explanation of the documents that the policy holder needs to send. */
	documentsExplanation?: string;
	/**
	 * Code of the form. Only used to determine all possible flexible fields in
	 * case a new field must be added. Otherwise only existing field can be
	 * updated.
	 *
	 * @maxLength 30
	 */
	formCode?: string;
	/** The general explanation of the claim. */
	generalExplanation?: string;
	/** Indicates whether the claim form has been signed by all parties. */
	isClaimFormSignedByAllParties?: boolean;
	/** Indicates whether the claim can be processed quickly. */
	isExpressClaim?: boolean;
	/** Indicates whether the claim is impactful. */
	isImpactfulClaim?: boolean;
	/**
	 * A remark about the claim.
	 *
	 * @maxLength 500
	 */
	remark?: string;
};

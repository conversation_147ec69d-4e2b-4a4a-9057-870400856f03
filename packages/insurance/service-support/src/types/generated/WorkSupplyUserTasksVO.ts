import type { WorkSupplyUserTasksDetailsVO } from "./WorkSupplyUserTasksDetailsVO";

export type WorkSupplyUserTasksVO = {
	/** This list contains all application process groups that a user belongs to. */
	applicationProcessGroupDetailsList?: Array<WorkSupplyUserTasksDetailsVO>;
	caseProcessGroupDetailsList?: Array<WorkSupplyUserTasksDetailsVO>;
	/**
	 * This element contains the user tasks belonging to the application
	 * processing group.
	 */
	intermediaryDetails?: WorkSupplyUserTasksDetailsVO;
	/** This field contains the user tasks belonging to the responsible user. */
	logonUserDetails?: WorkSupplyUserTasksDetailsVO;
	/**
	 * This field contains the number of user tasks in the external policy
	 * administration system that have not been assigned to a user.
	 */
	numberOfNotAssignedUserTasksInExternalPolicyAdministrationSystem?: number;
	/**
	 * This field determines whether to show an error message if no external user
	 * tasks could be found.
	 */
	showErrorMessageCouldNotRetrieveExternalUserTasks?: boolean;
	/**
	 * This field determines whether to show links to the external policy
	 * administration.
	 */
	showExternalPolicyAdministrationLinks?: boolean;
	/**
	 * This field contains the URL to the unassigned user tasks in the external
	 * policy administration.
	 *
	 * @maxLength 255
	 */
	urlToNotAssignedUserTasksInExternalPolicyAdministrationSystem?: string;
	/** This field contains the user tasks belonging to the department. */
	userDepartmentDetails?: WorkSupplyUserTasksDetailsVO;
};

export type WorkSupplyReserveGroupDetailVO = {
	/** This field contains the external identifier of the claim processing group. */
	externalIdentifier?: string;
	/**
	 * This field contains the name of the claim processing group / responsible
	 * user.
	 */
	name?: string;
	/** This field contains the number of reserves with status "To fiat". */
	nrOfReservesFiat?: number;
	/** This field contains the number of reserves with status "Rejected". */
	nrOfReservesRejected?: number;
	priorityExtId?: string;
	/**
	 * This field contains the total number of open reserves belonging to this
	 * claim process group / responsible user.
	 */
	totalNrOfReserves?: number;
};

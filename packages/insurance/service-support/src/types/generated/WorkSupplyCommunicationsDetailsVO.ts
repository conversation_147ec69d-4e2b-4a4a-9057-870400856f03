import type { WorkSupplyUrgencyVO } from "./WorkSupplyUrgencyVO";

export type WorkSupplyCommunicationsDetailsVO = {
	applicationProcessGroupExtId?: string;
	caseProcessGroupExtId?: string;
	chatsInProgressWithIntermediary?: WorkSupplyUrgencyVO;
	chatsInProgressWithInternalUser?: WorkSupplyUrgencyVO;
	communicationOriginExtIdOutgoingCommunications?: string;
	communicationsIncomingInProgress?: WorkSupplyUrgencyVO;
	inboxForCommunicationExtId?: string;
	includeLowerIntermediaries?: boolean;
	intermediaryExtId?: string;
	logonUserExtId?: string;
	/** This field contains the name of the processing group. */
	name?: string;
	/**
	 * This field contains the number of chats with status "in progress" with
	 * intermediaries
	 */
	nrOfChatsWithIntermediariesInProgress?: number;
	/**
	 * This field contains the number of chats with status "in progress" with
	 * other internal users
	 */
	nrOfChatsWithInternalUsersInProgress?: number;
	nrOfCommunicationsContactRegistrationInProgress?: number;
	nrOfCommunicationsContactRegistrationInProgressAssignedToUser?: number;
	nrOfCommunicationsContactRegistrationInProgressNotAssigned?: number;
	nrOfCommunicationsIncomingInProgressAssignedToUser?: number;
	/**
	 * This field contains the number of incoming communications with status "in
	 * progress" that are sent by email.
	 */
	nrOfCommunicationsIncomingInProgressEmail?: number;
	nrOfCommunicationsIncomingInProgressNotAssigned?: number;
	nrOfCommunicationsIncomingInProgressUserIsAcceptorOfRequest?: number;
	/**
	 * This field contains the number of outbound communications with status "to
	 * be sent"
	 */
	nrOfCommunicationsLocalOutboundToBeSent?: number;
	/**
	 * This field contains the number of outbound communications with status "in
	 * progress"
	 */
	nrOfCommunicationsOutboundInProgress?: number;
	nrOfUndeliveredDeliverableCommunications?: number;
	originExtId?: string;
	priorityExtId?: string;
	/**
	 * This field contains the total number of communications that belong to this
	 * processing group / responsible user.
	 */
	totalNrOfCommunications?: number;
};

export type VerifyExtSystemAuthorisationByIntermediaryVO = {
	/** The number of the intermediary/agent. */
	intermediaryNumber: string;
	/**
	 * This field contains the password of the external claim party.
	 *
	 * @maxLength 255
	 */
	passwordForService: string;
	/**
	 * Use for system-to-system authentication. This disables checking whether the
	 * intermediary-number is equal to the username.
	 */
	useForSystemToSystem?: boolean;
	/**
	 * This field contains the user name of the external claim party.
	 *
	 * @maxLength 255
	 */
	usernameForService: string;
};

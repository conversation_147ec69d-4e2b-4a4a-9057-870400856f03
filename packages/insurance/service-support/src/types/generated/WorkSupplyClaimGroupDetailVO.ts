export type WorkSupplyClaimGroupDetailVO = {
	/**
	 * This field contains the external identifier of the claim processing group /
	 * responsible user.
	 */
	externalIdentifier?: string;
	/**
	 * This field contains the name of the claim processing group / responsible
	 * user.
	 */
	name?: string;
	/** This field contains the number of claims with status "Created". */
	nrOfClaimsCreated?: number;
	/**
	 * This field contains the number of claims with (nearly) expired checklist
	 * items.
	 */
	nrOfClaimsNearlyExpiredChecklistItems?: number;
	/**
	 * This field contains the number of claims with policy changes
	 * retrospectively.
	 */
	nrOfClaimsPolicyChange?: number;
	/** This field contains the number of preliminary claims. */
	nrOfClaimsPreliminary?: number;
	/** This field contains the number of claims with special cases. */
	nrOfClaimsSpecialCase?: number;
	nrOfClaimsWithAllRequestedInformationDocumentsReceived?: number;
	/** This field contains the number of claims with indications to review. */
	nrOfClaimsWithIndicationToReview?: number;
	nrOfClaimsWithRequestedInformationToReview?: number;
	/**
	 * This field contains the total number of claims in this claim process group
	 * / responsible user.
	 */
	totalNrOfClaims?: number;
};

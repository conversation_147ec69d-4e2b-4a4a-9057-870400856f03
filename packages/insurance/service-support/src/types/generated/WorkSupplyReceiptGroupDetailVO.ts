export type WorkSupplyReceiptGroupDetailVO = {
	/** This field contains the external identifier of the claim processing group. */
	externalIdentifier?: string;
	/**
	 * This field contains the name of the claim processing group / responsible
	 * user.
	 */
	name?: string;
	/** This field contains the number of receipts with status "In Progress". */
	nrOfReceiptsInProcess?: number;
	/** This field contains the number of receipts with status "Read". */
	nrOfReceiptsRead?: number;
	/**
	 * This field contains the total number of open receipts belonging to this
	 * claim process group / responsible user.
	 */
	totalNrOfReceipts?: number;
};

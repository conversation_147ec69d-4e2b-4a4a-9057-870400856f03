import type { WorkSupplyUserTasksGroupDetailVO } from "./WorkSupplyUserTasksGroupDetailVO";

export type WorkSupplyUserTasksDetailsVO = {
	/** This field contains external identifier of the application process group. */
	applicationProcessGroupExternalIdentifier?: string;
	caseProcessGroupExternalIdentifier?: string;
	/**
	 * This list contains all user task groups that the responsible user belongs
	 * to.
	 */
	groupList?: Array<WorkSupplyUserTasksGroupDetailVO>;
	includeLowerIntermediaries?: boolean;
	intermediaryExternalIdentifier?: string;
	/**
	 * This field contains the name of the responsible user, department, or
	 * application process group that the object is referring to.
	 */
	name?: string;
	/** This field contains the number of open user tasks. */
	numberOfUserTasks?: number;
	/**
	 * This field contains the number of user tasks in the external policy
	 * administration.
	 */
	numberOfUserTasksInExternalPolicyAdministrationSystem?: number;
	/** This field contains the number of open user tasks present in Axon. */
	numberOfUserTasksInQIS?: number;
	/** This field contains the number of highly urgent user tasks. */
	numberOfUserTasksWithHighUrgency?: number;
	/** This field contains the number of user tasks of least urgency. */
	numberOfUserTasksWithLowUrgency?: number;
	/** This field contains the number of user tasks of lower urgency. */
	numberOfUserTasksWithMediumUrgency?: number;
	priorityExtId?: string;
	/** This field contains user identifier of the responsible user. */
	responsibleLogonUserUserIdentifier?: string;
	/**
	 * This field contains the URL to the user tasks in the external policy
	 * administration.
	 *
	 * @maxLength 255
	 */
	urlToUserTasksInExternalPolicyAdministrationSystem?: string;
	/** This field contains the external identifier of the department. */
	userDepartmentExternalIdentifier?: string;
};

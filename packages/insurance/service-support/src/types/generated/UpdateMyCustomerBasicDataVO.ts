import type { CountryRefVO } from "./CountryRefVO";

export type UpdateMyCustomerBasicDataVO = {
	/**
	 * This field contains the chamber of commerce number of the (business) party.
	 *
	 * @maxLength 20
	 */
	chamberOfCommerceNumber?: string;
	/** Technical field that indicates when the data was last updated. */
	changeToken?: string;
	/**
	 * This field contains the initials of the contact person of the (business)
	 * party.
	 *
	 * @maxLength 12
	 */
	contactPersonInitials?: string;
	/**
	 * This field contains the prefixes of the contact person of the (business)
	 * party.
	 *
	 * @maxLength 10
	 */
	contactPersonInsertion?: string;
	/**
	 * This field contains the name of the contact person of the (business) party.
	 *
	 * @maxLength 70
	 */
	contactPersonName?: string;
	/** This field contains the date of birth of the party. */
	dateOfBirth: string;
	/**
	 * This field contains the e-mail address of the party.
	 *
	 * @maxLength 320
	 */
	emailAddress1?: string;
	/**
	 * This field contains the fax number of the party.
	 *
	 * @maxLength 25
	 */
	faxNumber?: string;
	/**
	 * This field contains the first name of the party.
	 *
	 * @maxLength 50
	 */
	firstName?: string;
	/**
	 * This field contains the gender of the party.
	 *
	 * @maxLength 1
	 */
	gender?: string;
	/** This field indicates if the party has an open task. */
	hasUserTaskNotCompleted?: boolean;
	/**
	 * This field contains the initials of the party.
	 *
	 * @maxLength 12
	 */
	initials: string;
	/**
	 * This field contains the prefixes of the party.
	 *
	 * @maxLength 10
	 */
	insertion?: string;
	/**
	 * This field indicates if the party is a company (true) or a natural person
	 * (false).
	 */
	isCompany?: boolean;
	/** This field indicates if the party is externally managed. */
	isExternallyManaged?: boolean;
	/**
	 * This field contains the surname of the party.
	 *
	 * @maxLength 80
	 */
	lastName: string;
	/**
	 * This field contains participant number loyalty program of the party.
	 *
	 * @maxLength 12
	 */
	memberNumberLoyaltyProgram?: string;
	/**
	 * This field contains the national company number of the (business) party.
	 *
	 * @maxLength 20
	 */
	nationalCompanyNumber?: string;
	/**
	 * This field contains the title of the party.
	 *
	 * @maxLength 35
	 */
	prefix?: string;
	/**
	 * This field indicates if the party receives its invoices in its internet
	 * banker’s environment.
	 */
	receivesInvoicesInOnlBanking?: boolean;
	/** This field contains the country of registration of the customer. */
	registrationCountryRef?: CountryRefVO;
	/**
	 * This field contains the road-organization-member-number of the party.
	 *
	 * @maxLength 10
	 */
	roadOrganizationMemberNumber?: string;
	/**
	 * This field contains the Norwegian social security number of the party.
	 *
	 * @maxLength 11
	 */
	socialSecurityNumber?: string;
	/**
	 * This field contains the business telephone number of the (business) party.
	 *
	 * @maxLength 25
	 */
	telephoneNumberBusiness?: string;
	/**
	 * This field contains the mobile telephone number of the party.
	 *
	 * @maxLength 25
	 */
	telephoneNumberMobile?: string;
	/**
	 * This field contains the private telephone number of the party.
	 *
	 * @maxLength 25
	 */
	telephoneNumberPrivate?: string;
};

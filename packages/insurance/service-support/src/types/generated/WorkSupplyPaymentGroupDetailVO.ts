export type WorkSupplyPaymentGroupDetailVO = {
	/**
	 * This field contains the external identifier of the claim process group /
	 * responsible user.
	 */
	externalIdentifier?: string;
	/**
	 * This field contains the name of the claim processing group / responsible
	 * user.
	 */
	name?: string;
	/** This field contains the number of payments with status "To check". */
	nrOfPaymentsCheck?: number;
	/** This field contains the number of payments with status "To fiat". */
	nrOfPaymentsFiat?: number;
	/** This field contains the number of payments with status "In progress". */
	nrOfPaymentsInProcess?: number;
	/** This field contains the number of payments with status "Rejected". */
	nrOfPaymentsRejected?: number;
	/**
	 * This field contains the total number of open payments in this claim process
	 * group / responsible user.
	 */
	totalNrOfPayments?: number;
};

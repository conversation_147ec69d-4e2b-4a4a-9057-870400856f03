export type UpdateUserTaskVO = {
	/**
	 * This field contains a date which determines the latest date that the user
	 * task should be completed.
	 */
	dateToBeCompleted?: string;
	/**
	 * This field contains a character sequence with the description of the user
	 * task that will replace the old one.
	 */
	description?: string;
	/**
	 * This field contains the external identifier of the user task that is going
	 * to be updated.
	 */
	externalIdentifier?: string;
	/**
	 * This field contains a boolean which decides whether the user task is marked
	 * as cancelled.
	 */
	isCancelled?: boolean;
	/**
	 * This field contains a boolean which decides whether the user task is marked
	 * as completed.
	 */
	isCompleted?: boolean;
};

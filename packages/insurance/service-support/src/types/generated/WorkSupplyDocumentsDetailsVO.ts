export type WorkSupplyDocumentsDetailsVO = {
	caseProcessGroupExtId?: string;
	/**
	 * This field contains the name of the claim processing group / responsible
	 * user.
	 */
	name?: string;
	/** This field contains the number of documents with status "In progress". */
	nrOfDocumentsInProgress?: number;
	/** This field contains the number of documents with status "Rejected". */
	nrOfDocumentsRejected?: number;
	/** This field contains the number of documents with status "To be approved". */
	nrOfDocumentsToBeApproved?: number;
	/** This field contains the number of documents with status "To be shared". */
	nrOfDocumentsToBeShared?: number;
	responsibleLogonUserExtId?: string;
	/**
	 * This field contains the total number of documents belonging to the claim
	 * process group / responsible user.
	 */
	totalNrOfDocuments?: number;
};

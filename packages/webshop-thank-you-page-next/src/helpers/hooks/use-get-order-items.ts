import type { ConfirmedOrderDetailsItemsResponse } from "@anwb/webshop-data-queries";
import type { AnwbProductTypes, CartType } from "@anwb/webshop-types";

import { removeLocalStorage } from "@anwb/poncho/utilities/browser";
import { GET_CONFIRMED_ORDER_DETAILS_ITEMS } from "@anwb/webshop-data-queries";
import {
	CART_CONSTANTS,
	getL2Category,
	getL4Category,
} from "@anwb/webshop-helpers";
import { useQuery } from "@apollo/client";
import { useEffect, useMemo } from "react";

import { THANK_YOU_PAGE_CONSTANTS } from "../constants";

type UseGetOrderItemsProps = {
	maskedOrderId?: string;
	skip?: boolean;
};

export type OrderItem = {
	discounts: Array<{
		amount: {
			value: number;
		};
	}>;
	mainImageUrl: null | string;
	marketplaceShop: null | {
		name: string;
		shopId: number;
		targetUrl: string;
	};
	product: {
		__typename: "ConfigurableProduct" | "SimpleProduct";
		anwbMediaGallery: null | {
			mainImage: {
				altText: null | string;
				url: string;
			};
		};
		anwbProductType: {
			sub: AnwbProductTypes;
		};
		brand: Array<{
			value: string;
		}>;
		categoryFullName: string;
		deliveryTime: Array<{ value: string }>;
		ean: Array<{
			value: string;
		}>;
		gender: Array<{
			value: string;
		}>;
		isDropShipment: { value: boolean };
		labels: Array<{
			label: string;
			type: string;
		}>;
		productGroup: Array<{
			__typename: "CustomAnwbAttribute";
			code: string;
			value: string;
		}>;
		ratingSummary: number;
		reviewCount: number;
		sku: string;
		stockStatus: "IN_STOCK" | "OUT_OF_STOCK";
		targetUrl: string;
		vignetOptions: Array<{
			label: string;
			value: string;
		}>;
	};
	productName: string;
	productSalePrice: {
		value: number;
	};
	productSku: string;
	quantityOrdered: number;
	selectedOptions: Array<{
		label: string;
		value: string;
	}>;
};

export type OrderResponse = {
	__typename: "CustomerOrder";
	billingAddress: {
		city: string;
		firstname: string;
		lastname: string;
		middlename: null | string;
		postcode: string;
		street: [Array<string>];
	};
	email: string;
	items: Array<OrderItem>;
	number: string;
	paymentMethods: Array<{ name: string }>;
	shippingAddress: {
		city: string;
		firstname: string;
		lastname: string;
		middlename: null | string;
		postcode: string;
		street: [Array<string>];
	};
	total: {
		discounts: Array<{
			amount: {
				currency: string;
				value: number;
			};
			label: string;
		}>;
		extraFees: Array<{
			__typename: "ExtraFee";
			amount: {
				__typename: "Money";
				currency: "EUR";
				value: number;
			};
			code: string;
			label: string;
		}>;
		grandTotal: {
			value: number;
		};
		taxes: [
			{
				amount: {
					value: number;
				};
			},
		];
		totalShipping: {
			value: number;
		};
	};
};

export type TransformedOrderItem = {
	dataLayer: {
		brand: string;
		categoryFullName: string;
		discounts: OrderItem["discounts"];
		ean: string | undefined;
		gender: string;
		item_category2: string;
		item_category3: string;
		item_category4: string;
		labels: OrderItem["product"]["labels"];
		productGroup: string;
		ratingSummary: number;
		reviewCount: number;
		stockStatus: OrderItem["product"]["stockStatus"];
	};
	deliveredBy: string;
	imageAlt: null | string | undefined;
	imageUrl: string | undefined;
	isDropShipment: boolean;
	options: Array<{
		label: string;
		value: string;
	}>;
	parentSku: OrderItem["productSku"];
	price: number;
	productType: AnwbProductTypes;
	quantity: number;
	sellerName: string;
	sellersUrl: string | undefined;
	sku: OrderItem["product"]["sku"];
	title: string;
};

export type TransformedOrderResponse = Omit<
	ConfirmedOrderDetailsItemsResponse["order"],
	"items"
> & {
	cartType: OrderItemType;
	items: Array<TransformedOrderItem>;
};

export type OrderItemType = "DROP_SHIPMENT" | "MIRAKL" | "MIXED" | "REGULAR";

export function useGetOrderItems(props: UseGetOrderItemsProps) {
	const { maskedOrderId, skip = false } = props;

	const {
		called: hasBeenCalled,
		data: orderItemsQueryData,
		error: orderItemsQueryError,
		loading: isOrderItemsLoading,
	} = useQuery<ConfirmedOrderDetailsItemsResponse>(
		GET_CONFIRMED_ORDER_DETAILS_ITEMS,
		{
			fetchPolicy: "cache-and-network",
			skip: !maskedOrderId || skip,
			variables: { orderMaskId: maskedOrderId },
		},
	);

	useEffect(() => {
		if (orderItemsQueryError) {
			return;
		}

		if (orderItemsQueryData) {
			// Remove masked order id from local storage if order is successful.
			// It's set in local storage to handle the back button on the payment page.
			removeLocalStorage(THANK_YOU_PAGE_CONSTANTS.maskedOrderIdLocalStorageKey);
			removeLocalStorage(CART_CONSTANTS.localStorageKey);
			// Todo: get this from checkout constants
			removeLocalStorage("anwb-webshop-checkout-information");
		}
	}, [orderItemsQueryError, orderItemsQueryData]);

	const modifiedOrderItems = useMemo((): {
		cartType: OrderItemType;
		items: Array<TransformedOrderItem>;
	} => {
		if (!orderItemsQueryData?.order || orderItemsQueryError) {
			return {
				cartType: "MIXED",
				items: [],
			};
		}

		const { categories, modifiedOrderItems } = (
			orderItemsQueryData.order.items || []
		).reduce<{
			categories: Set<OrderItemType>;
			modifiedOrderItems: Array<TransformedOrderItem>;
		}>(
			(acc, item) => {
				// In a rare case if there is a error due to a backend upgrade, the backend might return null instead of an item.
				if (!item) {
					return acc;
				}

				let itemType: OrderItemType;
				const productGroup = item.product.productGroup?.[0]?.value;
				const transformedItem: TransformedOrderItem = {
					dataLayer: {
						brand: item.product.brand?.[0]?.value ?? "",
						categoryFullName: item.product.categoryFullName,
						discounts: item.discounts,
						ean: item.product.ean?.[0]?.value ?? undefined,
						gender: item.product.gender?.[0]?.value ?? "",
						item_category2:
							getL2Category(
								productGroup ?? item.product.anwbProductType?.sub,
							) ?? "",
						item_category3: getL4Category(productGroup) ?? "",
						item_category4: productGroup ?? "",
						labels: item.product.labels,
						productGroup:
							productGroup ?? item.product.anwbProductType?.sub ?? "",
						ratingSummary: item.product.ratingSummary,
						reviewCount: item.product.reviewCount,
						stockStatus: item.product.stockStatus,
					},
					deliveredBy: item.product.deliveryTime[0]?.value ?? "-",
					imageAlt: item.product.anwbMediaGallery?.mainImage.altText,
					imageUrl:
						item.mainImageUrl ?? item.product.anwbMediaGallery?.mainImage.url,
					isDropShipment: item.product.isDropShipment.value,
					options: [...item.selectedOptions, ...item.product.vignetOptions],
					parentSku: item.productSku,
					price: item.productSalePrice.value,
					productType: item.product.anwbProductType.sub,
					quantity: item.quantityOrdered,
					sellerName: item.marketplaceShop?.name ?? "ANWB",
					sellersUrl: item.marketplaceShop?.targetUrl,
					sku: item.product.sku,
					title: item.productName,
				};

				if (item.marketplaceShop) {
					itemType = "MIRAKL";
				} else if (transformedItem.isDropShipment) {
					itemType = "DROP_SHIPMENT";
				} else {
					itemType = "REGULAR";
				}

				acc.modifiedOrderItems.push(transformedItem);
				acc.categories.add(itemType);

				return acc;
			},
			{ categories: new Set(), modifiedOrderItems: [] },
		);

		modifiedOrderItems.sort((itemA, itemB) => {
			if (itemA.sellerName === "ANWB") {
				return -1;
			}

			if (itemB.sellerName === "ANWB") {
				return 1;
			}

			return itemA.sellerName.localeCompare(itemB.sellerName);
		});

		let cartType: CartType;
		const categoriesArray = [...categories];

		if (categoriesArray.length === 1 && categoriesArray[0]) {
			cartType = categoriesArray[0];
		} else {
			cartType = "MIXED";
		}

		return {
			cartType,
			items: modifiedOrderItems,
		};
	}, [orderItemsQueryData?.order, orderItemsQueryError]);

	return {
		cartType: modifiedOrderItems?.cartType ?? undefined,
		hasBeenCalled,
		isOrderItemsLoading,
		orderError: orderItemsQueryError,
		orderItems: modifiedOrderItems?.items ?? [],
	};
}

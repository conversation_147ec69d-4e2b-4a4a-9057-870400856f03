import type { ConfirmedOrderDetailsResponse } from "@anwb/webshop-data-queries";

import {
	createDiscountsString,
	getProductSellerType,
	mapCartTypeToGA,
	pushEventPurchase,
} from "@anwb/webshop-data-layer";
import {
	type Item,
	type Purchase,
} from "@anwb/webshop-data-layer/google-analytics-4";

import type { OrderItemType, TransformedOrderItem } from "./hooks";

export const pushLiveChatEvent = (
	orderDetails: ConfirmedOrderDetailsResponse["order"],
	orderItems: Array<TransformedOrderItem>,
): void => {
	const eventProducts = orderItems.map((item) => {
		return {
			product: {
				category: item.dataLayer.categoryFullName,
				name: item.title,
				price: item.price,
			},
			quantity: item.quantity,
		};
	});

	window.postMessage(
		{
			action: "purchase",
			data: {
				orderId: orderDetails.number,
				products: eventProducts,
				total: orderDetails.total.grandTotal.value,
			},
		},
		window.location.origin,
	);
};

export function handleOrderConfirmationAnalytics(
	orderDetails: ConfirmedOrderDetailsResponse["order"],
	orderItems: Array<TransformedOrderItem>,
	cartType?: OrderItemType,
) {
	const purchase: Purchase = {
		ecommerce: {
			affiliation: "Webwinkel",
			checkout_type: mapCartTypeToGA(cartType ?? null),
			coupon: createDiscountsString(orderDetails.total.discounts) ?? "",
			currency: "EUR",
			items: orderItems.map(transformOrderItem),
			shipping: orderDetails.total.totalShipping.value,
			tax: orderDetails.total.taxes[0]?.amount.value ?? 0,
			transaction_id: orderDetails.number,
			value: orderDetails.total.grandTotal.value ?? 0,
		},
		event: "purchase",
	};

	pushEventPurchase(purchase);

	return null;
}

function transformOrderItem(item: TransformedOrderItem): Item {
	return {
		affiliation: "Webwinkel",
		currency: "EUR",
		custom_item_parameter1: item.sellerName,
		custom_item_parameter2: item.dataLayer.reviewCount,
		custom_item_parameter3: item.dataLayer.ratingSummary,

		custom_item_parameter4: item.dataLayer.labels?.some(
			(label) => label.type === "online",
		),
		custom_item_parameter5: item.dataLayer.labels
			?.map((label) => label.label)
			.join(","),
		delivery_time: item.deliveredBy,
		discount: item.dataLayer.discounts?.[0]?.amount.value ?? 0,
		gender: item.dataLayer.gender,
		item_brand: item.dataLayer.brand,
		item_category: "Webwinkel",
		item_category2: item.dataLayer.item_category2,
		item_category3: item.dataLayer.item_category3,
		item_category4: item.dataLayer.item_category4,
		item_ean: item.dataLayer.ean || null,
		item_id: item.sku,
		item_name: item.title ?? "",
		item_parent_sku: item.parentSku,
		item_sku: item.sku,

		item_stock: item.dataLayer.stockStatus === "IN_STOCK",
		item_variant:
			item.options.find(({ label }) =>
				label.toLocaleLowerCase().includes("kleur"),
			)?.value ?? "",
		member_discount: -1,
		price: item.price,
		seller_type: getProductSellerType({
			isDropshipment: item.isDropShipment,
			isMiraklProduct: item.sellerName !== "ANWB",
		}),
	};
}

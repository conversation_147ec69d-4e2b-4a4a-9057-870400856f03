import type { Columns } from "../../types";

import { renderBloomreachDocument } from "../../../../../utils/renderBloomreachDocument";
import { Application } from "../../../../documents/Application";
import { Contact } from "../../../../documents/Contact";
import { Panel as ContentItem } from "../../../../documents/ContentItem";
import { Counter } from "../../../../documents/Counter";
import { PanelReview } from "../../../../documents/PanelReview";
import { Promotion } from "../../../../documents/Promotion";
import { Trustpilot } from "../../../../documents/Trustpilot";
import { Widget } from "../../../../documents/Widget";
import { getTransforms } from "./helpers/getTransforms";
import { type DocumentData, DocumentDataSchema } from "./schemas";

type Props = DocumentData & {
	/** The amount of columns */
	columns: Columns;
};

/**
 * The collection content document
 *
 * This is a conditional component that renders the correct document based on
 * the content type that's part of the document data. It passes the props to the
 * document and also adds the `transformations` prop to the `<ContentItem />`
 * document.
 */
export function UnionDocument(props: Props) {
	switch (props.contentType) {
		case "anwb:applicationdocument":
			return <Application {...props} />;
		case "anwb:contactdocument":
			return <Contact {...props} />;
		case "anwb:panelreviewdocument":
			return <PanelReview {...props} />;
		case "anwb:promotiondocument":
			return (
				<Promotion {...props} transformations={getTransforms(props.columns)} />
			);
		case "anwb:tellerdocument":
			return <Counter {...props} />;
		case "anwb:trustpilotdocument":
			return <Trustpilot {...props} />;
		case "anwb:widgetdocument":
			return <Widget {...props} useMobileView />;
		case "bricks:contentitem":
			return (
				<ContentItem
					{...props}
					transformations={getTransforms(props.columns)}
				/>
			);
	}
}

/**
 * The Bloomreach collection content document
 *
 * This component is wrapped by the `renderBloomreachDocument` function to
 * provide the necessary Bloomreach context to the document.
 */
export const BrUnionDocument = renderBloomreachDocument(
	UnionDocument,
	DocumentDataSchema,
);

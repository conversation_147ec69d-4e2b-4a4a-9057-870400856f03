import { z } from "zod";

import { DocumentDataSchema as ApplicationSchema } from "../../../../documents/Application";
import { DocumentDataSchema as ContactSchema } from "../../../../documents/Contact";
import { DocumentDataSchema as ContentItemSchema } from "../../../../documents/ContentItem";
import { DocumentDataSchema as CounterSchema } from "../../../../documents/Counter";
import { DocumentDataSchema as PanelReviewSchema } from "../../../../documents/PanelReview";
import { DocumentDataSchema as PromotionSchema } from "../../../../documents/Promotion";
import { DocumentDataSchema as TrustpilotSchema } from "../../../../documents/Trustpilot";
import { DocumentDataSchema as WidgetSchema } from "../../../../documents/Widget";

export const DocumentDataSchema = z.union([
	ApplicationSchema,
	ContactSchema,
	ContentItemSchema,
	CounterSchema,
	PanelReviewSchema,
	PromotionSchema,
	WidgetSchema,
	TrustpilotSchema,
]);

export type DocumentData = z.infer<typeof DocumentDataSchema>;

import { generateUniqueIdForBlueconic } from "@anwb/platform-support";
import { useApplicationSize } from "@anwb/poncho/providers/providers-application";
import { InformationStrip as PonchoInformationStrip } from "@anwb/poncho/widgets/information-strip";

import type { ComponentData } from "../../../types";
import type { Models, Parameters } from "./types";

import { BrContainer } from "../../../components/BrContainer";
import { useRelevanceTracking } from "../../../hooks/useRelevanceTracking";
import { getContainerSpacing } from "../../../utils/getContainerSpacing";
import { renderBloomreachComponent } from "../../../utils/renderBloomreachComponent";
import { ButtonsMapper } from "./components/ButtonsMapper";
import { Wrapper } from "./components/Wrapper";
import { BrUnionDocument } from "./documents/UnionDocument";
import * as Styled from "./styled";

/**
 * The information strip component
 *
 * This is one of the Bloomreach components that can be set up in the Bloomreach
 * experience manager. This component will render information strip documents
 * within the `<BrContainer />` component. The information strip documents are
 * provided via the `documents` parameter as a collection of references.
 */
export function InformationStrip({
	documents,
	hidden,
	highlightedPositions,
	id,
	name,
	precededByCampaignBanner,
	relevance,
	spacing: brSpacing,
	targetId,
	title,
}: ComponentData<Parameters, Models>) {
	const tracking = useRelevanceTracking("information-strip", relevance);

	const size = useApplicationSize();

	return (
		<BrContainer
			className={generateUniqueIdForBlueconic({ id, name })}
			data-optimization={targetId || undefined}
			hidden={hidden}
			withConstrainedWidth
			withOuterSpacing={getContainerSpacing(
				brSpacing,
				precededByCampaignBanner ? { left: true, right: true } : undefined,
			)}
			{...tracking}
		>
			<Wrapper inCampaignBanner={precededByCampaignBanner}>
				{title && <Styled.Title variant="content-title">{title}</Styled.Title>}
				<PonchoInformationStrip>
					<PonchoInformationStrip.Buttons>
						<ButtonsMapper
							documents={documents}
							highlightedPositions={highlightedPositions}
						/>
					</PonchoInformationStrip.Buttons>
					{documents.map((document, index) => {
						if (size !== "large" && highlightedPositions.includes(index)) {
							return null;
						}

						return (
							<BrUnionDocument
								data-position={index + 1}
								index={index}
								key={document.$ref}
								reference={document}
								totalChildren={documents.length}
							/>
						);
					})}
				</PonchoInformationStrip>
			</Wrapper>
		</BrContainer>
	);
}

/**
 * The Bloomreach information strip component
 *
 * This component is wrapped by the `renderBloomreachComponent` function to
 * provide the necessary Bloomreach context to the component.
 */
export const BrInformationStrip = renderBloomreachComponent(InformationStrip);

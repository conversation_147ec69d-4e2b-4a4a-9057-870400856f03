import { z } from "zod";

import { DocumentDataSchema as ApplicationSchema } from "../Application";
import { DocumentDataSchema as ContactSchema } from "../Contact";
import { DocumentDataSchema as ContentItemSchema } from "../ContentItem";
import { DocumentDataSchema as CounterSchema } from "../Counter";
import { MinimalDocumentDataSchema as MinimalLinkBlockSchema } from "../LinkBlock";
import { DocumentDataSchema as PanelReviewSchema } from "../PanelReview";
import { DocumentDataSchema as PromotionSchema } from "../Promotion";
import { DocumentDataSchema as TrustpilotSchema } from "../Trustpilot";
import { DocumentDataSchema as WidgetSchema } from "../Widget";

export const DocumentDataSchema = z.union([
	ApplicationSchema,
	ContactSchema,
	ContentItemSchema,
	CounterSchema,
	PanelReviewSchema,
	PromotionSchema,
	WidgetSchema,
	MinimalLinkBlockSchema,
	TrustpilotSchema,
]);

export type DocumentData = z.infer<typeof DocumentDataSchema>;

import { renderBloomreachDocument } from "../../../utils/renderBloomreachDocument";
import { Application } from "../Application";
import { Contact } from "../Contact";
import { Panel as ContentItem } from "../ContentItem";
import { Counter } from "../Counter";
import { PromotionPanel } from "../LinkBlock";
import { PanelReview } from "../PanelReview";
import { Promotion } from "../Promotion";
import { Trustpilot } from "../Trustpilot";
import { Widget } from "../Widget";
import { type DocumentData, DocumentDataSchema } from "./schemas";

export function Aside(props: DocumentData) {
	switch (props.contentType) {
		case "anwb:applicationdocument":
			return <Application {...props} />;
		case "anwb:contactdocument":
			return <Contact {...props} />;
		case "anwb:linkblockdocument":
			return <PromotionPanel {...props} />;
		case "anwb:panelreviewdocument":
			return <PanelReview {...props} />;
		case "anwb:promotiondocument":
			return <Promotion {...props} />;
		case "anwb:tellerdocument":
			return <Counter {...props} />;
		case "anwb:trustpilotdocument":
			return <Trustpilot {...props} />;
		case "anwb:widgetdocument":
			return <Widget {...props} useMobileView />;
		case "bricks:contentitem":
			return <ContentItem {...props} />;
	}
}

export const BrAside = renderBloomreachDocument(Aside, DocumentDataSchema);

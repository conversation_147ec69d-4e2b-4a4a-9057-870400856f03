import { renderBloomreachDocument } from "../../../utils/renderBloomreachDocument";
import { type DocumentData, DocumentDataSchema } from "./schemas";

/**
 * Data-template-id is an ANWB global for trustpilot.
 *
 * Data-businessunit-id is channel specific.
 */
export function Trustpilot({ trustpilotId, trustpilotName }: DocumentData) {
	return (
		<>
			<div
				className="trustpilot-widget"
				data-businessunit-id={trustpilotId}
				data-locale="nl-NL"
				data-style-height="150px"
				data-style-width="100%"
				data-template-id="53aa8807dec7e10d38f59f32"
				style={{ visibility: "visible" }}
			>
				<a
					href={`https://nl.trustpilot.com/review/anwb.nl/${trustpilotName}`}
					rel="noopener"
					target="_blank"
				>
					<></>
				</a>
			</div>
			<script
				defer
				src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js"
				type="text/javascript"
			></script>
		</>
	);
}

export const BrTrustpilot = renderBloomreachDocument(
	Trustpilot,
	DocumentDataSchema,
);

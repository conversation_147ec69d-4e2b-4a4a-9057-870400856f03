import { type MouseEvent, useCallback } from "react";
import { useInView } from "react-intersection-observer";

import type { Relevance } from "../../types";
import type { TrackingEvent } from "./types";

import { useBlueConicSegmentTracking } from "./hooks/useBlueConicSegmentTracking";
import { useDefaultTracking } from "./hooks/useDefaultTracking";
import { useOptimizelyTracking } from "./hooks/useOptimizelyTracking";
import { usePersonalizationTracking } from "./hooks/usePersonalizationTracking";

/**
 * Custom hook for tracking relevance events
 *
 * This will either use Optimizely, BlueConic or DGP to track the events
 */
export const useRelevanceTracking = <T extends Element>(
	component: string,
	{
		characteristic,
		characteristics,
		enabled,
		provider,
		trackClicks = false,
		trackViews = true,
	}: Relevance,
) => {
	const handleOptimizelyTracking = useOptimizelyTracking({
		provider,
		variant: characteristic,
	});
	const handleBlueConicSegmentTracking = useBlueConicSegmentTracking({
		component,
		segment: characteristic,
	});
	const handlePersonalizationTracking = usePersonalizationTracking({
		characteristics,
		component,
	});
	const handleDefaultTracking = useDefaultTracking({
		component,
	});

	const { ref, ...inViewProps } = useInView({
		fallbackInView: true,
		initialInView: true,
		onChange: (inView, _entry) => {
			if (!inView || !enabled || !trackViews) return;

			// Handle view event tracking for each characteristic (position)
			// This means if a component scrolls into view, we track the view events for all positions at once
			if (characteristics.length) {
				characteristics.forEach((variantCharacteristic) => {
					handleTracking("visibility", variantCharacteristic.position);
				});
			}
		},
		threshold: 0.2,
		triggerOnce: true,
	});

	const handleTracking = useCallback(
		(event: TrackingEvent, position: number) => {
			if (!enabled) return;

			if (provider === "blueconic") {
				handleBlueConicSegmentTracking(event);
			}

			if (provider === "personalization") {
				handlePersonalizationTracking(event, position);
			}

			if (
				provider === "optimizely" ||
				provider === "optimizelyfeatureflagcombined"
			) {
				handleOptimizelyTracking(event);
			}

			if (provider === "n/a") {
				handleDefaultTracking(event, position);
			}
		},
		[
			enabled,
			provider,
			handleBlueConicSegmentTracking,
			handlePersonalizationTracking,
			handleOptimizelyTracking,
			handleDefaultTracking,
		],
	);

	const onClickCapture = useCallback(
		(event: MouseEvent<T>) => {
			if (!enabled || !trackClicks) return;

			const componentRootElement = event.currentTarget;
			const clickedElement = event.target as HTMLElement;

			const positionedElement =
				clickedElement.closest<HTMLElement>("[data-position]");

			if (
				!positionedElement ||
				!componentRootElement.contains(positionedElement)
			) {
				return;
			}

			const { position } = positionedElement.dataset;

			if (!position) return;

			handleTracking("click", parseInt(position, 10));
		},
		[enabled, handleTracking, trackClicks],
	);

	return {
		entry: inViewProps.entry,
		inView: inViewProps.inView,
		onClickCapture: trackClicks ? onClickCapture : undefined,
		ref,
	};
};

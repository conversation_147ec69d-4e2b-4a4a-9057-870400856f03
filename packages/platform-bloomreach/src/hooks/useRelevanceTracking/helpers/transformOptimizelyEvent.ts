/** Interface representing an Optimizely tracking event */
type OptimizelyEvent = {
	/** The event type */
	event: "click" | "view_experiment";
	/** The experiment ID */
	experiment: string;
	/** The provider of the event */
	provider: string;
	/** The type of the event */
	type: string;
	/** The variant of the event */
	variant: string;
};

/**
 * Sanitize the data by removing the provider key.
 *
 * @param data The Optimizely event data
 * @returns The sanitized event data without the provider property
 */
const sanitizeData = (data: OptimizelyEvent) => {
	const { provider: _provider, ...sanitizedData } = data;

	return sanitizedData;
};

/**
 * Transform the Optimizely event data. When shouldTransform is true, it will
 * extract the base experiment and variant from the variant string and format
 * them appropriately for tracking.
 *
 * @param data The Optimizely event data to transform
 * @param shouldTransform Whether the data should be transformed (true for
 *   optimizelyfeatureflagcombined provider)
 * @returns The transformed event data with the provider property removed
 */
export const transformOptimizelyEvent = (
	data: OptimizelyEvent,
	shouldTransform: boolean,
) => {
	if (!shouldTransform) {
		return sanitizeData(data);
	}

	const [baseExperiment, experimentVariant] = data.variant.split("-#-");

	if (!baseExperiment || !experimentVariant) {
		return sanitizeData(data);
	}

	return sanitizeData({
		...data,
		experiment: baseExperiment,
		variant: `variant ${experimentVariant}`,
	});
};

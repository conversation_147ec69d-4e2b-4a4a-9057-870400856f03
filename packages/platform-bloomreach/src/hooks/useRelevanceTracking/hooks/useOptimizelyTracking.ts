import { trackEvent } from "@anwb/poncho/utilities/tracking";
import { useCallback } from "react";

import type { RelevanceProvider } from "../../../types";
import type { TrackingHandler } from "../types";

import { transformOptimizelyEvent } from "../helpers/transformOptimizelyEvent";
import { useBloomreachRelevance } from "./useBloomreachRelevance";

/** Base parameters for Optimizely tracking */
type Params = {
	/** The relevance provider */
	provider: RelevanceProvider;
	/** The variant of the component */
	variant: string;
};

/** Parameters for tracking Optimizely events */
type EventParams = Params & {
	/** The tracking event */
	event: "click" | "view_experiment";
	/** The experiment ID */
	experiment: string;
};

/**
 * Helper function for tracking Optimizely events
 *
 * @param params - The event parameters including provider, variant, event type
 *   and experiment ID
 */
const trackOptimizelyEvent = (params: EventParams) => {
	const { provider } = params;

	const isFeatureFlagsCombined = provider === "optimizelyfeatureflagcombined";

	const optimizelyEvent = transformOptimizelyEvent(
		{
			...params,
			type: "optimizely",
		},
		isFeatureFlagsCombined,
	);

	trackEvent(optimizelyEvent);
};

/**
 * Hook used for tracking Optimizely events
 *
 * @param params - The parameters including provider and variant
 * @returns A callback function that handles tracking different event types
 *   (click or visibility)
 */
export const useOptimizelyTracking = (params: Params) => {
	const { provider, variant } = params;
	const pageState = useBloomreachRelevance();

	// TODO: Remove newData when newData is removed
	const page = pageState.newData?.page ?? pageState.page;

	const experiment =
		provider === "optimizelyfeatureflagcombined"
			? variant
			: page.optimizelyFeatureFlags[0]?.featureFlagId || "";

	return useCallback<TrackingHandler>(
		(event) => {
			if (event === "click") {
				trackOptimizelyEvent({ event, experiment, ...params });
			}

			if (event === "visibility") {
				trackOptimizelyEvent({
					event: "view_experiment",
					experiment,
					...params,
				});
			}
		},
		[experiment, params],
	);
};

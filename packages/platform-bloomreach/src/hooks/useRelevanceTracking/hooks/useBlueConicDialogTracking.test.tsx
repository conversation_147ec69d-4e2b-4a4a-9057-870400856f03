import type {
	BlueConicClient,
	BlueConicInteraction,
} from "@anwb/platform-third-parties";

import { useBlueConicContext } from "@anwb/platform-third-parties";
import { trackEvent as mockPonchoTrackEvent } from "@anwb/poncho/utilities/tracking";
import { act, renderHook } from "@testing-library/react-hooks";
import { beforeEach, describe, expect, it, vi } from "vitest";

import type { TrackingEvent } from "../types";

import { CATEGORY_PERSONALIZED_BLUECONIC_DIALOGS } from "../constants";
import { filterByPosition } from "../helpers/filterByPosition";
import { transformComponentName } from "../helpers/transformComponentName";
import { useBloomreachRelevance } from "./useBloomreachRelevance";
import { useBlueConicDialogTracking } from "./useBlueConicDialogTracking";

// Mock setup
vi.mock("@anwb/poncho/utilities/tracking", () => ({
	trackEvent: vi.fn(),
}));

vi.mock("../helpers/transformComponentName", () => ({
	transformComponentName: vi.fn((name: string) => name.toLowerCase()),
}));

vi.mock("../helpers/filterByPosition", () => ({
	filterByPosition: vi.fn(),
}));

vi.mock("./useInvoke", () => ({
	useInvoke: <T extends (...args: Array<unknown>) => unknown>(fn: T): T => fn,
}));

vi.mock("@anwb/platform-third-parties");
vi.mock("./useBloomreachRelevance");
vi.mock("../helpers/trackPersonalizedEvent", () => ({
	trackPersonalizedEvent: vi.fn(
		(component: string, personalization: string, category: string) => {
			return ({
				event,
				position,
			}: {
				event: TrackingEvent;
				position: number;
			}) => {
				mockPonchoTrackEvent({
					category,
					component,
					event,
					personalization,
					position,
					type: "personalised-proposition",
				});
			};
		},
	),
}));

describe("useBlueConicDialogTracking", () => {
	// Test constants
	const TEST_COMPONENT_NAME = "TestComponent";
	const TEST_INTERACTION_ID = "interaction-1";
	const TEST_DIALOGUE_ID = "dialogue-1";
	const TEST_PERSONALIZATION = "MockInteraction";
	const TEST_NUMERIC_POSITION = 1;

	// Mock functions
	const mockCreateEvent = vi.fn();
	const mockGetName = vi.fn();
	const mockGetInteractionId = vi.fn();
	const mockGetDialogueId = vi.fn();
	const mockGetPosition = vi.fn();
	const mockSubscribe = vi.fn();

	// Test setup helpers
	const setupMockValues = () => {
		const position = `${transformComponentName(TEST_COMPONENT_NAME)}-${TEST_NUMERIC_POSITION}`;
		mockGetPosition.mockReturnValue(position);
		mockGetName.mockReturnValue(TEST_PERSONALIZATION);
		mockGetInteractionId.mockReturnValue(TEST_INTERACTION_ID);
		mockGetDialogueId.mockReturnValue(TEST_DIALOGUE_ID);
	};

	const createMockClient = (): BlueConicClient => ({
		createEvent: mockCreateEvent,
		event: {
			onBeforeInteractions: "onbeforeinteractions",
			onBeforePreListeners: "onbeforeprelisteners",
			onEventReady: "oneventready",
			onLifecycleStageChange: "onlifecyclestagechange",
			onProfilePermissionChange: "onpermissionlevelchange",
			onReady: "onready",
			onSegmentChange: "onsegmentchange",
			onUrlChange: "onurlchange",
			subscribe: mockSubscribe,
		},
		getHostname: () => "test.host",
		profile: {
			getProfile: () => ({
				getId: () => "test-id",
				setConsentedObjectives: vi.fn(),
				setRefusedObjectives: vi.fn(),
			}),
			updateProfile: vi.fn(),
		},
	});

	const createMockInteraction = (): BlueConicInteraction => ({
		context: {
			getDialogueId: mockGetDialogueId,
			getInteractionId: mockGetInteractionId,
			getName: mockGetName,
			getPosition: mockGetPosition,
		},
	});

	beforeEach(() => {
		vi.clearAllMocks();
		console.error = vi.fn();

		setupMockValues();

		vi.mocked(useBlueConicContext).mockReturnValue({
			client: createMockClient(),
			interactions: [createMockInteraction()],
		});

		vi.mocked(transformComponentName).mockImplementation((name) =>
			name.toLowerCase(),
		);

		vi.mocked(filterByPosition).mockImplementation((position) => {
			return (interaction) => {
				const interactionPosition = interaction.context.getPosition();
				return interactionPosition === position;
			};
		});

		vi.mocked(useBloomreachRelevance).mockReturnValue({
			components: [
				{
					componentName: TEST_COMPONENT_NAME,
					position: TEST_NUMERIC_POSITION,
					variant: "DEFAULT",
				},
			],
			identifier: "test-identifier",
			page: {
				functions: {
					auth: false,
					dialogs: true,
					optimizely: false,
					proposition: false,
					segments: false,
				},
				optimizelyFeatureFlags: [],
				segments: [],
			},
		});
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it("should track click events with BlueConic and Poncho", () => {
		const { result } = renderHook(() =>
			useBlueConicDialogTracking({
				component: TEST_COMPONENT_NAME,
			}),
		);

		act(() => {
			result.current("click" as TrackingEvent, TEST_NUMERIC_POSITION);
		});

		expect(mockGetName).toHaveBeenCalled();
		expect(mockPonchoTrackEvent).toHaveBeenCalledWith({
			category: CATEGORY_PERSONALIZED_BLUECONIC_DIALOGS,
			component: TEST_COMPONENT_NAME,
			event: "click",
			personalization: TEST_PERSONALIZATION,
			position: TEST_NUMERIC_POSITION,
			type: "personalised-proposition",
		});
		expect(mockCreateEvent).toHaveBeenCalledWith("CLICK", TEST_INTERACTION_ID);
	});

	it("should track view events with BlueConic and Poncho", () => {
		const { result } = renderHook(() =>
			useBlueConicDialogTracking({
				component: TEST_COMPONENT_NAME,
			}),
		);

		act(() => {
			result.current("view" as TrackingEvent, TEST_NUMERIC_POSITION);
		});

		expect(mockGetName).toHaveBeenCalled();
		expect(mockPonchoTrackEvent).toHaveBeenCalledWith({
			category: CATEGORY_PERSONALIZED_BLUECONIC_DIALOGS,
			component: TEST_COMPONENT_NAME,
			event: "view",
			personalization: TEST_PERSONALIZATION,
			position: TEST_NUMERIC_POSITION,
			type: "personalised-proposition",
		});
		expect(mockCreateEvent).toHaveBeenCalledWith("VIEW", TEST_INTERACTION_ID);
	});

	it("should log error when BlueConic client is not available", () => {
		vi.mocked(useBlueConicContext).mockReturnValueOnce({
			client: undefined,
			interactions: [],
		});

		const { result } = renderHook(() =>
			useBlueConicDialogTracking({
				component: TEST_COMPONENT_NAME,
			}),
		);

		act(() => {
			result.current("click" as TrackingEvent, TEST_NUMERIC_POSITION);
		});

		expect(console.error).toHaveBeenCalledWith(
			"[Sombrero] BlueConic tracking prerequisites not met",
		);
		expect(mockPonchoTrackEvent).not.toHaveBeenCalled();
		expect(mockCreateEvent).not.toHaveBeenCalled();
	});

	it("should log error when no interactions are available", () => {
		vi.mocked(useBlueConicContext).mockReturnValueOnce({
			client: createMockClient(),
			interactions: [],
		});

		const { result } = renderHook(() =>
			useBlueConicDialogTracking({
				component: TEST_COMPONENT_NAME,
			}),
		);

		act(() => {
			result.current("click" as TrackingEvent, TEST_NUMERIC_POSITION);
		});

		expect(console.error).toHaveBeenCalledWith(
			"[Sombrero] BlueConic tracking prerequisites not met",
		);
		expect(mockPonchoTrackEvent).not.toHaveBeenCalled();
		expect(mockCreateEvent).not.toHaveBeenCalled();
	});

	it("should log error when interaction is not found for position", () => {
		vi.mocked(filterByPosition).mockReturnValueOnce(() => false);

		const { result } = renderHook(() =>
			useBlueConicDialogTracking({
				component: TEST_COMPONENT_NAME,
			}),
		);

		act(() => {
			result.current("click" as TrackingEvent, TEST_NUMERIC_POSITION);
		});

		expect(console.error).toHaveBeenCalledWith(
			"[BlueConic] Could not find interaction for position",
			TEST_NUMERIC_POSITION,
		);
		expect(mockPonchoTrackEvent).not.toHaveBeenCalled();
		expect(mockCreateEvent).not.toHaveBeenCalled();
	});
});

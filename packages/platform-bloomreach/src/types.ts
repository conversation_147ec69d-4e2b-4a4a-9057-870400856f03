import type { MenuItem } from "@bloomreach/spa-sdk";

import type { Reference } from "./schemas";

export type PersonalizedComponent = {
	componentName: string;
	position: number;
	variant: string;
};

export type FeatureFlag = {
	featureFlagId: string;
	variants?: Array<string>;
};

export type PageFunctions = {
	auth: boolean;
	dialogs: boolean;
	optimizely: boolean;
	proposition: boolean;
	segments: boolean;
};

export type Page = {
	functions: PageFunctions;
	optimizelyFeatureFlags: Array<{
		featureFlagId: string;
	}>;
	segments: Array<string>;
};

export type ComponentData<Parameters = object, Models = object> = Models &
	Omit<Parameters, keyof Models> & {
		/** The unique identifier of the component */
		id: string;
		/** The name of the component */
		name: string;
	};

/** Container Spacing used for combination of Bloomreach components. */
export type BrContainerSpacing = "add" | "default" | "none";

export type PreflightAttributes = {
	components: Array<PersonalizedComponent>;
	identifier: string;
	// TODO: Remove legacy pageState when possible
	newData?: {
		components: Array<PersonalizedComponent>;
		identifier: string;
		page: Page;
	};
	page: Page;
};

export type PropsWithMenuItems<Props = object> = Props & {
	/** The Bloomreach menu items */
	menuItems: Array<MenuItem>;
};

export type PropsWithMenuItem<Props = object> = Props & {
	/** The Bloomreach menu item */
	menuItem: MenuItem;
};

export type PropsWithReference<Props = object> = Props & {
	/** The Bloomreach reference */
	reference: Reference;
};

export type RelevanceProvider =
	| "blueconic"
	| "n/a"
	| "optimizely"
	| "optimizelyfeatureflagcombined"
	| "personalization";

export type Relevance = {
	/**
	 * The relevance characteristic which will be used for tracking the user
	 * interaction.
	 *
	 * ```text
	 * ┌───────────────────────────────┬─────────────────────────────────────────────────┐
	 * │ provider                      │ characteristic                                  │
	 * ├───────────────────────────────┼─────────────────────────────────────────────────┤
	 * │ blueconic                     │ UUID ("090f9056-d282-4abb-ac4b-b5105058a73b")   │
	 * │ blueconicdialogs              │ [{ componentName: "header", position: [2, 4] }] │
	 * │ optimizely                    │ Selected variant ("variant A")                  │
	 * │ optimizelyfeatureflagcombined │ Selected variants ("variant A" + "variant B")   │
	 * │ n/a                           │ "n/a"                                           │
	 * └───────────────────────────────┴─────────────────────────────────────────────────┘
	 * ```
	 */
	characteristic: string;
	/** The characteristics of the variants in the component */
	characteristics: Array<VariantCharacteristic>;
	/** If the relevance tracking is enabled */
	enabled: boolean;
	/** If the component is hidden */
	hidden: boolean;
	/** The relevance provider */
	provider: RelevanceProvider;
	/** Whether to track click events. Defaults to true. */
	trackClicks?: boolean;
	/** Whether to track view events. Defaults to true. */
	trackViews?: boolean;
};

export type Variant = "DEFAULT" | "FALLBACK" | "PERSONALIZED";

export type VariantCharacteristic = {
	characteristic: string;
	position: number;
	variant: Variant;
};

export type ComponentPosition = "first" | "last" | "middle";

import gql from "graphql-tag";

export const GET_CONFIRMED_ORDER_DETAILS = gql`
	query getConfirmedOrderDetails($orderMaskId: String!) {
		order(orderMaskId: $orderMaskId) {
			__typename
			number
			billingAddress {
				firstname
				middlename
				lastname
				street
				postcode
				city
			}
			shippingAddress {
				firstname
				middlename
				lastname
				street
				postcode
				city
			}
			email
			paymentMethods {
				name
			}
			shippingMethod
			total {
				extraFees {
					code
					label
					amount {
						currency
						value
					}
				}
				discounts {
					amount {
						currency
						value
					}
					label
				}
				taxes {
					amount {
						value
					}
				}
				totalShipping {
					value
				}
				grandTotal {
					value
				}
				shippingHandling {
					totalAmount {
						value
					}
				}
			}
		}
	}
`;

export const GET_CONFIRMED_ORDER_DETAILS_ITEMS = gql`
	query getConfirmedOrderDetailsItems($orderMaskId: String!) {
		order(orderMaskId: $orderMaskId) {
			__typename
			number
			items {
				__typename
				mainImageUrl
				marketplaceShop {
					shopId
					name
					targetUrl
				}
				productSku
				productName
				productSalePrice {
					value
				}
				quantityOrdered
				selectedOptions {
					label
					value
				}

				#### this data is fetched only for analytics

				discounts {
					amount {
						value
					}
				}

				product {
					__typename
					isDropShipment: customAnwbAttributes(
						attributes: ["anwb_is_dropshipment"]
					) {
						value
					}
					categoryFullName
					### Todo: Ruen check if this is needed
					anwbProductType {
						sub
					}
					anwbMediaGallery {
						mainImage {
							url
							altText
						}
					}
					targetUrl
					vignetOptions: customAnwbAttributes(
						attributes: [
							"geldigheidsduur_vignet"
							"vignet_geldigheidsduur_text"
						]
					) {
						label
						value
					}
					deliveryTime: customAnwbAttributes(attributes: ["anwb_levertijd"]) {
						value
					}

					... on ConfigurableProduct {
						anwbMediaGallery {
							mainImage {
								url
								altText
							}
						}
					}
					#### this data is only fetched for analytics
					gender: customAnwbAttributes(attributes: ["anwb_sexe"]) {
						value
					}
					ean: customAnwbAttributes(attributes: ["anwb_ean"]) {
						value
					}
					brand: customAnwbAttributes(attributes: ["anwb_merk_brand"]) {
						value
					}

					reviewCount
					ratingSummary
					labels {
						label
						type
						value
					}
					categoryFullName
					sku
					stockStatus
					productGroup: customAnwbAttributes(
						attributes: ["anwb_product_attribuut_set"]
					) {
						code
						value
					}
				}
			}
		}
	}
`;

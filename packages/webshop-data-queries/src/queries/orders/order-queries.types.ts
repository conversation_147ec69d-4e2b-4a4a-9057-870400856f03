import type { AnwbProductTypes } from "@anwb/webshop-types";

export type ConfirmedOrderDetailsResponse = {
	order: {
		__typename: "CustomerOrder";
		billingAddress: {
			city: string;
			firstname: string;
			lastname: string;
			middlename: null | string;
			postcode: string;
			street: [Array<string>];
		};
		email: string;
		number: string;
		paymentMethods: Array<{ name: string }>;
		shippingAddress: {
			city: string;
			firstname: string;
			lastname: string;
			middlename: null | string;
			postcode: string;
			street: [Array<string>];
		};
		total: {
			discounts: Array<{
				amount: {
					currency: string;
					value: number;
				};
				label: string;
			}>;
			extraFees: Array<{
				__typename: "ExtraFee";
				amount: {
					__typename: "Money";
					currency: "EUR";
					value: number;
				};
				code: string;
				label: string;
			}>;
			grandTotal: {
				value: number;
			};
			taxes: [
				{
					amount: {
						value: number;
					};
				},
			];
			totalShipping: {
				value: number;
			};
		};
	};
};

export type ConfirmedOrderDetailsItemsResponse = {
	order: {
		__typename: "CustomerOrder";
		items: Array<OrderItem>;
		number: string;
	};
};

export type OrderItem = {
	discounts: Array<{
		amount: {
			value: number;
		};
	}>;
	mainImageUrl: null | string;
	marketplaceShop: null | {
		name: string;
		shopId: number;
		targetUrl: string;
	};
	product: {
		__typename: "ConfigurableProduct" | "SimpleProduct";
		anwbMediaGallery: null | {
			mainImage: {
				altText: null | string;
				url: string;
			};
		};
		anwbProductType: {
			sub: AnwbProductTypes;
		};
		brand: Array<{
			value: string;
		}>;
		categoryFullName: string;
		deliveryTime: Array<{ value: string }>;
		ean: Array<{
			value: string;
		}>;
		gender: Array<{
			value: string;
		}>;
		isDropShipment: { value: boolean };
		labels: Array<{
			label: string;
			type: string;
		}>;
		productGroup: Array<{
			__typename: "CustomAnwbAttribute";
			code: string;
			value: string;
		}>;
		ratingSummary: number;
		reviewCount: number;
		sku: string;
		stockStatus: "IN_STOCK" | "OUT_OF_STOCK";
		targetUrl: string;
		vignetOptions: Array<{
			label: string;
			value: string;
		}>;
	};
	productName: string;
	productSalePrice: {
		value: number;
	};
	productSku: string;
	quantityOrdered: number;
	selectedOptions: Array<{
		label: string;
		value: string;
	}>;
};

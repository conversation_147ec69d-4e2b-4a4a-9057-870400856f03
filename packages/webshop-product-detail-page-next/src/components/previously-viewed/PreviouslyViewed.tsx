import Typography from "@anwb/poncho/components/typography";
import ProductsRow from "@anwb/webshop-products-row";

import { useProductState } from "../../context";
import { PreviouslyViewedContainer } from "./styles/previously-viewed.styled";
import usePreviouslyViewedItems from "./usePreviouslyViewedItems";

function PreviouslyViewed() {
	const { product, selectedVariant } = useProductState();
	const { getPreviouslyViewedItems } = usePreviouslyViewedItems();

	const listName = "Eerder door jou bekeken";

	if (!product) return null;

	const currentSku = selectedVariant?.colorConfigurableSku ?? product?.sku;

	const products = getPreviouslyViewedItems(currentSku);

	if (products.length === 0) return null;

	return (
		<PreviouslyViewedContainer withConstrainedWidth>
			<Typography as="h2" variant="content-title">
				{listName}
			</Typography>
			<ProductsRow eventList={listName} products={products} />
		</PreviouslyViewedContainer>
	);
}

export default PreviouslyViewed;

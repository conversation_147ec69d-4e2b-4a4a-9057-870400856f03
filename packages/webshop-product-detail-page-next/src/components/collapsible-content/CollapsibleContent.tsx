import { ButtonTertiary } from "@anwb/poncho/components/button";
import Typography from "@anwb/poncho/components/typography";
import { type ReactNode, useEffect, useRef, useState } from "react";

import {
	useProductDetailDispatch,
	useProductDetailState,
} from "../../context/product-detail/context";
import {
	CollapsibleContentContainer,
	CollapsibleContentFooter,
	CollapsibleContentInnerContainer,
	GradientOverlay,
} from "./styles/collapsible-content.styled";

type CollapsibleContentProps = {
	buttonText?: string;
	children: ReactNode;
	maxHeight?: number;
	maxHeightEnabled?: boolean;
	panelKey: "information" | "reviews" | "specifications";
	title: string;
};

function CollapsibleContent({
	buttonText = "Toon meer",
	children,
	maxHeight = 165,
	maxHeightEnabled = true,
	panelKey,
	title,
}: CollapsibleContentProps) {
	const { accordionPanels } = useProductDetailState();
	const dispatch = useProductDetailDispatch();

	const [isOverflowing, setIsOverflowing] = useState(false);

	const contentRef = useRef<HTMLDivElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const panel = accordionPanels[panelKey];

	if (!panel?.isVisible) return null;

	useEffect(() => {
		if (panel.scrollIntoView) {
			containerRef.current?.scrollIntoView({ behavior: "smooth" });
		}
	}, [panel]);

	useEffect(() => {
		if (contentRef.current && maxHeightEnabled) {
			setIsOverflowing(contentRef.current.scrollHeight > maxHeight);
		}
	}, [maxHeight, children, maxHeightEnabled]);

	const currentMaxHeight =
		panel.isOpen || !maxHeightEnabled ? "none" : `${maxHeight}px`;

	const shouldShowGradient = !panel.isOpen && isOverflowing && maxHeightEnabled;

	return (
		<CollapsibleContentContainer ref={containerRef}>
			{title && (
				<Typography as="h2" variant="content-title">
					{title}
				</Typography>
			)}

			<CollapsibleContentInnerContainer
				ref={contentRef}
				style={{ maxHeight: currentMaxHeight }}
			>
				{children}
				{shouldShowGradient && <GradientOverlay />}
			</CollapsibleContentInnerContainer>

			{shouldShowGradient && (
				<CollapsibleContentFooter>
					<ButtonTertiary
						onClick={() => {
							dispatch({ panel: panelKey, type: "SET_ACCORDION_PANEL_OPEN" });
						}}
					>
						{buttonText}
					</ButtonTertiary>
				</CollapsibleContentFooter>
			)}
		</CollapsibleContentContainer>
	);
}

export default CollapsibleContent;

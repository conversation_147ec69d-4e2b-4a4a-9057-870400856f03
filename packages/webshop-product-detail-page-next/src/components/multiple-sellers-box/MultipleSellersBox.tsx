import Panel from "@anwb/poncho/components/panel";
import Typography from "@anwb/poncho/components/typography";
import { AnwbProductTypes } from "@anwb/webshop-types";
import { useState } from "react";

import { useProductState } from "../../context";
import MultipleSellersOverlay from "../multiple-seller-overlay/MultipleSellersOverlay";
import {
	MultipleSellersLink,
	SellerPageLink,
	SellerText,
	SellerUspList,
	SellerWrapper,
} from "./styles/multiple-sellers-list.styled";

function MultipleSellerBox() {
	const { product, selectedVariant, selections } = useProductState();

	if (!product) return null;

	const [openOverlay, setOpenOverlay] = useState(false);

	const seller = selectedVariant?.seller || product.seller;

	const hasValidSelection = product.options.every(
		(option) => selections[option.code],
	);

	const sellerData = {
		...seller,
		otherOffers: selectedVariant?.otherOffers || product.otherOffers,
		totalOffers: selectedVariant?.totalOffers || product.totalOffers,
	};

	return (
		<>
			<Panel data-name="multiple-sellers-list" hasOuterSpacing={false}>
				<Panel.Content>
					<SellerWrapper $hideBorder={sellerData.otherOffers?.length === 0}>
						{sellerData?.otherOffers && sellerData?.otherOffers?.length > 0 && (
							<SellerText>Verkoop door ANWB partner</SellerText>
						)}
						{sellerData.sellerTitle &&
							(sellerData.link ? (
								<SellerPageLink
									disableIcon
									href={`/webwinkel/${sellerData.link}`}
								>
									{sellerData.name}
								</SellerPageLink>
							) : (
								<Typography as="p" variant="component-title">
									{sellerData.sellerTitle}
								</Typography>
							))}

						{sellerData?.totalOffers > 1 &&
							hasValidSelection &&
							product.anwbProductType !== AnwbProductTypes.TICKET && (
								<MultipleSellersLink
									onClick={() => {
										setOpenOverlay(true);
									}}
								>
									Bij {sellerData.totalOffers} partner
									{sellerData.totalOffers > 1 && "s"} verkrijgbaar
								</MultipleSellersLink>
							)}
					</SellerWrapper>
					{sellerData?.usps && (
						<SellerUspList>
							{sellerData?.usps?.map((usp, index) => (
								<SellerUspList.Item key={index + usp}>{usp}</SellerUspList.Item>
							))}
						</SellerUspList>
					)}
				</Panel.Content>
			</Panel>
			<MultipleSellersOverlay
				openOverlay={openOverlay}
				setOpenOverlay={setOpenOverlay}
			/>
		</>
	);
}

export default MultipleSellerBox;

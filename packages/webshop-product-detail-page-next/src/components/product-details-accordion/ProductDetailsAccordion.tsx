import { Tooltip } from "@anwb/poncho/components/tooltip";
import Typography from "@anwb/poncho/components/typography";
import { useMemo } from "react";

import type { SpecificationGroup } from "../../helpers/sortSpecificationGroups";

import { useProductState } from "../../context";
import { sortSpecificationGroups } from "../../helpers/sortSpecificationGroups";
import { CollapsibleContent } from "../collapsible-content";
import ProductSpecificationsTable from "../product-specification-table/ProductSpecificationTable";
import Reviews from "../reviews/Reviews";
import { ProductInformationText } from "./styles/product-details-accordion.styled";

function ProductDetailsAccordion() {
	const { product } = useProductState();
	// iterate all specifications and group them based on... group :)
	const specificationGroups: Array<SpecificationGroup> = useMemo(
		() =>
			product?.specifications
				.reduce((groups: Array<SpecificationGroup>, customAttribute) => {
					const existingGroup = groups.find(
						({ code }) => code === customAttribute.group?.code,
					);
					if (!existingGroup) {
						groups.push({
							code: customAttribute.group?.code,
							label: customAttribute.group?.label,
						});
					}
					return groups;
				}, [])
				.sort(sortSpecificationGroups) || [],
		[product],
	);

	return (
		<>
			<CollapsibleContent
				buttonText="Toon alle productinformatie"
				panelKey="information"
				title="Productinformatie"
			>
				<ProductInformationText
					/* backend sanitizes content and only allows/returns html tags */
					dangerouslySetInnerHTML={{
						__html: product?.description.html || "",
					}}
				/>
			</CollapsibleContent>

			{product &&
				specificationGroups.map((group) => {
					if (!group?.label || !group.code) {
						return null;
					}

					// GPRS table has a specific header...
					// TODO: see if we can add notifications as metadata for groups or something?
					// it would make all groups uniform in presentation
					if (group.code.toLowerCase() === "gpsr") {
						return (
							<CollapsibleContent
								key={group.code}
								maxHeightEnabled={false}
								panelKey="specifications"
								title=""
							>
								<Typography as="h2" variant="content-title">
									{group.label}
									<Tooltip>
										<Typography variant="tooltip-title">Let op</Typography>
										<Typography variant="tooltip-text">
											De verantwoordelijke marktdeelnemer in de EU is ervoor
											verantwoordelijk dat de verplichtingen op het gebied van
											productveiligheid worden nagekomen. De onderstaande
											gegevens kunnen anders zijn dan de contactgegevens van de
											verkoper. Je kunt een artikel daarom mogelijk niet
											terugsturen naar dit adres. Voor vragen over het artikel
											of de levering (behalve productveiligheid of herkomst),
											neem contact op met de verkoper.
										</Typography>
									</Tooltip>
								</Typography>
								<ProductSpecificationsTable
									specifications={product.specifications.filter(
										(spec) => spec.group?.code === group.code,
									)}
								/>
							</CollapsibleContent>
						);
					}

					return (
						<CollapsibleContent
							buttonText={`Toon alle ${group.label}`}
							key={group.code}
							panelKey="specifications"
							title={group.label}
						>
							<ProductSpecificationsTable
								specifications={product.specifications.filter(
									(spec) => spec.group?.code === group.code,
								)}
							/>
						</CollapsibleContent>
					);
				})}

			<CollapsibleContent
				maxHeightEnabled={false}
				panelKey="reviews"
				title="Reviews"
			>
				<Reviews />
			</CollapsibleContent>
		</>
	);
}

export default ProductDetailsAccordion;

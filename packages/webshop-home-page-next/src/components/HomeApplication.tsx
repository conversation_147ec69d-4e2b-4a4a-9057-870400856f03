import { Accordion } from "@anwb/poncho/components/accordion";
import { ButtonSecondary } from "@anwb/poncho/components/button";
import Grid from "@anwb/poncho/components/grid";
import List from "@anwb/poncho/components/list";
import Panel, { PanelReview } from "@anwb/poncho/components/panel";
import Typography from "@anwb/poncho/components/typography";
import { VisuallyHidden } from "@anwb/poncho/components/visually-hidden";
import {
	BannerWebshop,
	BannerWebshopImage,
	BannerWebshopText,
	BannerWebshopTitle,
} from "@anwb/webshop-banner";
import { Footer } from "@anwb/webshop-components";
import {
	pushEventPromotionClick,
	pushEventPromotionImpression,
} from "@anwb/webshop-data-layer";
import WebshopNavHeader from "@anwb/webshop-navigation-header";
import { PrismicSlice } from "@anwb/webshop-prismic-slice";

import type { HomePageApplicationData } from "../helper/types";

import { StyledHomePage } from "../Styles";
import { FaqAccordion } from "./FaqAccordion";
import MobileNav from "./MobileNav";
import { ProductTop } from "./ProductTop";

const DataLayerPageId = "home";

export function HomeApplication({
	cmsContent = { body: [], faq: [] },
	footerContent,
	productCategories = [],
}: HomePageApplicationData) {
	const handlePromotionClickEvent = ({
		creative,
		destinationURL,
	}: {
		creative: string;
		destinationURL: string;
	}) => {
		pushEventPromotionClick({
			creative: creative || "",
			destinationURL,
			id: DataLayerPageId,
			name: `${DataLayerPageId} - header`,
			position: 1,
			type: "header",
			widgetRow: 1,
		});
	};

	const handleBannerIsVisible = (text: string) => {
		pushEventPromotionImpression({
			component: "banner",
			position: 1,
			text,
			type: "header",
			widgetRow: 1,
		});
		pushEventPromotionImpression({
			component: "button",
			position: 1,
			text,
			type: "header",
			widgetRow: 1,
		});
	};

	return (
		<>
			<VisuallyHidden>
				<h1>ANWB Webwinkel</h1>
			</VisuallyHidden>
			{!cmsContent?.hide_submenu && (
				<WebshopNavHeader productCategories={productCategories} />
			)}
			<StyledHomePage>
				<BannerWebshop
					ctaButton={
						cmsContent.header_cta_link &&
						(cmsContent.header_cta_color_inverted ? (
							<ButtonSecondary
								href={
									cmsContent.header_cta_link.slug
										? `/webwinkel/${cmsContent.header_cta_link.slug}`
										: cmsContent.header_cta_link.url
								}
								onClick={() => {
									handlePromotionClickEvent({
										creative:
											cmsContent.header_cta_label ||
											cmsContent.header_title ||
											"Shop nu",
										destinationURL: cmsContent.header_cta_link.url,
									});
								}}
							>
								{cmsContent.header_cta_label || "Shop nu"}
							</ButtonSecondary>
						) : (
							<ButtonSecondary
								href={
									cmsContent.header_cta_link.slug
										? `/webwinkel/${cmsContent.header_cta_link.slug}`
										: cmsContent.header_cta_link.url
								}
								onClick={() => {
									handlePromotionClickEvent({
										creative:
											cmsContent.header_cta_label || cmsContent.header_title,
										destinationURL: cmsContent.header_cta_link.url,
									});
								}}
								variant="on-dark"
							>
								{cmsContent.header_cta_label || "Shop nu"}
							</ButtonSecondary>
						))
					}
					// @ts-expect-error todo: fix this type
					gradientOverlay={cmsContent.gradient_overlay}
					image={
						cmsContent.header_image && (
							<BannerWebshopImage
								images={{
									default: cmsContent.header_image,
								}}
								isLazy={false}
							/>
						)
					}
					isVisible={() => {
						handleBannerIsVisible(cmsContent.header_cta_label || "Shop nu");
					}}
					link={cmsContent.header_cta_link && cmsContent.header_cta_link.url}
					noPadding
					reverseColor={cmsContent.header_cta_color_inverted}
					variant="header"
					width="full"
				>
					<BannerWebshopTitle>{cmsContent.header_title}</BannerWebshopTitle>
					<BannerWebshopText>{cmsContent.header_text}</BannerWebshopText>
				</BannerWebshop>

				<MobileNav
					navLinks={cmsContent.mobile_nav_links}
					showLessLabel={cmsContent.mobile_nav_show_less}
					showMoreLabel={cmsContent.mobile_nav_show_more}
				/>

				{cmsContent.body.map((slice, i) => (
					<PrismicSlice
						dataLayer={{ pageId: DataLayerPageId, row: i + 1 }}
						items={slice.items}
						key={`slice-${slice.id}`}
						primary={slice.primary}
						sliceType={slice.slice_type}
					/>
				))}
				<Grid constrainedWidth={true}>
					<Grid.Item>
						<div className="WEBSHOP-service">
							<ProductTop
								length={cmsContent?.top_products_length}
								templateId={cmsContent?.top_products_tweakwise_id}
								title={
									cmsContent &&
									cmsContent.top_products_title &&
									cmsContent.top_products_title[0].text
								}
							/>
							<div className="WEBSHOP-service__right">
								<Panel
									// @ts-expect-error todo: fix this type
									title={cmsContent?.service_title}
									variant="featured"
								>
									<Panel.Header
										headerLevel="h2"
										title={cmsContent?.service_title}
									/>
									<Panel.Content>
										<List
											items={cmsContent.service_points?.map(
												(point) => point.text,
											)}
											variant="checked"
										/>
									</Panel.Content>
								</Panel>
								<PanelReview
									link={cmsContent.review_cta_label}
									rating={cmsContent.review_rating}
									ratings={cmsContent.review_ratings}
									title={cmsContent.review_title}
									variant="featured"
								/>
							</div>
						</div>
						<div style={{ padding: "0.8rem" }}>
							<Typography as="h2" variant="content-title">
								Categorieën
							</Typography>
							<Accordion>
								{cmsContent.faq.map((item) => (
									<FaqAccordion item={item} key={`faq-item-${item.question}`} />
								))}
							</Accordion>
						</div>
					</Grid.Item>
					<Grid.Aside isSticky={false}>
						{cmsContent.panels &&
							cmsContent.panels.map((panel) => (
								<Panel
									className="WEBSHOP-home--cms-panel"
									href={
										panel.link.link_type === "Document"
											? `/webwinkel/${panel.link.slug}`
											: panel.link.url
									}
									key={`panel-${panel.title}-${panel.image.url}`}
									variant="featured"
								>
									<Panel.Media
										image={{ alt: panel.image.alt || "", src: panel.image.url }}
									/>
									<Panel.Header title={panel.title} />
								</Panel>
							))}
					</Grid.Aside>
				</Grid>
			</StyledHomePage>
			{footerContent && <Footer {...footerContent} />}
		</>
	);
}

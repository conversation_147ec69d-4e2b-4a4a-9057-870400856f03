import type { ProductRowItem } from "@anwb/webshop-products-row";
import type {
	ApiTypes,
	Item,
	Product,
	RecommendationType,
} from "@anwb/webshop-types";

import Typography from "@anwb/poncho/components/typography";
import {
	tweakwiseRecommendationsFeatured,
	tweakwiseRecommendationsProductGroup,
} from "@anwb/webshop-data-queries";
import {
	mapTileProducts,
	useTweakwiseRecommendation,
} from "@anwb/webshop-helpers";
import { ProductTileAddToCart } from "@anwb/webshop-product-tile";
import ProductsRow from "@anwb/webshop-products-row";
import { useEffect, useState } from "react";

const defaultTypeTitles: Record<RecommendationType, string> = {
	crossSell: "Dit past hier goed bij",
	featured: "Uitgelichte producten",
	upsell: "Vergelijkbare artikelen",
};

const recommendationQuery: Record<RecommendationType, any> = {
	crossSell: tweakwiseRecommendationsProductGroup,
	featured: tweakwiseRecommendationsFeatured,
	upsell: tweakwiseRecommendationsProductGroup,
};

export type Props = {
	groupCode?: string;
	handleAddProductToCart?: (
		product: Product,
		colorID?: string,
		sizeID?: string,
	) => void;
	templateId: number;
	title?: string;
	tweakwiseItemNumber?: string;
	type: RecommendationType;
};

export function ProductRecommendations({
	groupCode,
	handleAddProductToCart,
	templateId,
	tweakwiseItemNumber,
	type,
	...props
}: Props) {
	const title = props.title ?? defaultTypeTitles[type];

	const [products, setProducts] = useState<Array<ProductRowItem>>([]);

	const [getProducts, { loading }] = useTweakwiseRecommendation({
		groupCode,
		options: {
			onCompleted: (data) => {
				if (!data) return;
				let receivedProducts = [];
				if (data?.tweakwiseRecommendationsFeatured)
					receivedProducts = data.tweakwiseRecommendationsFeatured;
				if (data?.tweakwiseRecommendationsProduct)
					receivedProducts = data.tweakwiseRecommendationsProduct;
				if (data?.tweakwiseRecommendationsProductGroup?.[0]?.items)
					receivedProducts = data.tweakwiseRecommendationsProductGroup.flatMap(
						(i: ApiTypes.TweakwiseRecommendation) => i.items,
					);

				const uniqueProducts = [
					...new Map<number, Product>(
						receivedProducts.map((item: Item) => [
							item.product.id,
							item.product,
						]),
					).values(),
				];

				const tiles = mapTileProducts(uniqueProducts as any) as Array<
					Product & {
						image: ApiTypes.AnwbMediaGalleryItem;
					}
				>;

				setProducts(
					tiles
						.map((product) => {
							if (product.name) {
								return {
									brand: product.brand,
									category: product.category,
									colorCount: product.colorCount ?? null,
									configurableColor: null,
									ean: product.ean,
									extraComponent:
										type === "crossSell" && handleAddProductToCart ? (
											// TODO: I believe the shared ProductTile can be removed.
											//       If so, the ProductTileAddToCart has to be moved to this component.
											<ProductTileAddToCart
												handleAddProductToCart={handleAddProductToCart}
												product={product}
											/>
										) : null,
									gender: product.gender,
									id: product.id,

									image: product.image,
									labels: product.labels,
									name: product.name,
									price: null,
									priceStore: product.priceStore,
									productGroup: product.productGroup,
									reviewCount: product.reviewCount,
									reviewRating: product.reviewRating,
									seller: product.seller,
									sku: product.sku,

									targetUrl: product.targetUrl,
								};
							}
						})
						.filter(Boolean) as Array<ProductRowItem>,
				);
			},
		},
		query: recommendationQuery[type],
		templateId,
		tweakwiseItemNumber,
		type,
	});

	useEffect(() => {
		getProducts().catch(console.error);
	}, [getProducts]);

	if (loading || products.length === 0) {
		return null;
	}

	return (
		<>
			<Typography as="h2" variant="content-title">
				{title}
			</Typography>
			<ProductsRow
				eventList={title}
				products={products}
				showMoreColorsAvailableLabel
			/>
		</>
	);
}

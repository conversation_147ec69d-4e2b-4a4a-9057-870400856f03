import type { ProductCategory } from "@anwb/webshop-types";

import Icon from "@anwb/poncho/components/icon";
import Typography from "@anwb/poncho/components/typography";
import { getLocalStorage } from "@anwb/poncho/utilities/browser";
import { useCartQuantity } from "@anwb/webshop-helpers";
import { useEffect, useState } from "react";

import SearchBox from "./components/SearchBox";
import { SearchContainer } from "./components/SearchContainer";
import {
	CHANGE_CART_MESSAGE_TYPE,
	ShoppingCartWidget,
} from "./components/ShoppingCartWidget";
import { GlobalStyle } from "./GlobalStyles";

const WEBSHOP_CART_COUNT = "anwb-webshop-cart-item-count";

type Props = {
	categoryTreeOpen?: boolean;
	hideShoppingCartIcon?: boolean;
	productCategories: Array<ProductCategory>;
	totalCartQuantity?: number;
};

export function WebshopNavHeader({
	productCategories = [],
	totalCartQuantity,
}: Props) {
	const { cartQuantity } = useCartQuantity();

	const totalQuantity = totalCartQuantity ?? cartQuantity;
	const [quantity, setQuantity] = useState<number>(totalQuantity);

	useEffect(() => {
		const checkQuantity = (): void => {
			const localQuantity = Number(getLocalStorage(WEBSHOP_CART_COUNT));
			if (!isNaN(localQuantity)) setQuantity(localQuantity);
		};
		const checkQuantityMessage = (event: MessageEvent): void => {
			if (event.data.type === CHANGE_CART_MESSAGE_TYPE) {
				setQuantity(event?.data?.count || 0);
			}
		};

		checkQuantity();

		window.addEventListener("storage", checkQuantity);
		window.addEventListener("message", checkQuantityMessage);
		return () => {
			window.removeEventListener("storage", checkQuantity);
			window.removeEventListener("message", checkQuantityMessage);
		};
	}, []);

	useEffect(() => {
		window.postMessage({
			count: totalQuantity,
			type: CHANGE_CART_MESSAGE_TYPE,
		});
		setQuantity(totalQuantity);
	}, [totalQuantity]);

	return (
		<>
			<GlobalStyle />
			<section className="WEBSHOP-nav-header">
				<nav className="WEBSHOP-nav-header__content" role="navigation">
					<div
						className="WEBSHOP-nav-header__dropdown"
						id="nav-header-dropdown-menu"
						tabIndex={0}
					>
						<i className="WEBSHOP-nav-header__dropdown-shadow" />
						<Typography
							className="WEBSHOP-nav-header__dropdown-link"
							tabindex={0}
							tagName="h2"
							variant="component-title"
						>
							<div className="WEBSHOP-nav-header__dropdown-label">
								<Icon variant="menu" /> Categorieën
							</div>
						</Typography>
						<ul className="WEBSHOP-nav-header__dropdown-menu">
							{productCategories
								.filter((category) => category?.includeInMenu)
								.map((pc) => (
									<li
										className="WEBSHOP-nav-header__dropdown-menu-item PONCHO-typography PONCHO-typography--body-text"
										key={pc.id}
									>
										<Typography
											className="WEBSHOP-nav-header__dropdown-menu-item-link"
											href={pc.targetUrl}
											tagName="a"
											variant="button-link"
										>
											{pc.name}
										</Typography>
									</li>
								))}
						</ul>
					</div>
					<SearchContainer>
						<SearchBox
							// @ts-expect-error todo: fix this type
							className="WEBSHOP-nav-header__search-container"
						/>
					</SearchContainer>
					<div className="WEBSHOP-nav-header__usp">
						<Icon variant="check-usp" />
						<Typography tagName="span">
							Voor leden: Gratis verzending vanaf 50,-
						</Typography>
					</div>
					<div className="WEBSHOP-nav-header__favourites">
						<Icon
							onClick={() =>
								(window.location.href = "/mijn-anwb/mijn-favorieten")
							}
							size="lg"
							variant="favourite-outline"
						/>
					</div>
					<div
						className="WEBSHOP-nav-header__shopping-cart"
						style={{ display: "none" }}
					>
						<ShoppingCartWidget quantity={quantity} />
					</div>
				</nav>
			</section>
		</>
	);
}

export default WebshopNavHeader;

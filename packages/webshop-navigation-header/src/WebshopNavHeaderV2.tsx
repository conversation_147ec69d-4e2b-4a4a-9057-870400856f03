import type { ProductCategoryV2 as ProductCategory } from "@anwb/webshop-types";

import Icon from "@anwb/poncho/components/icon";
import {
	BodyText,
	ButtonLink,
	ComponentTitle,
} from "@anwb/poncho/components/typography";
import { getLocalStorage } from "@anwb/poncho/utilities/browser";
import { useCartQuantity } from "@anwb/webshop-helpers";
import { useEffect, useState } from "react";

import SearchBox from "./components/SearchBox";
import { SearchContainer } from "./components/SearchContainer";
import {
	CHANGE_CART_MESSAGE_TYPE,
	ShoppingCartWidget,
} from "./components/ShoppingCartWidget";
import { GlobalStyle } from "./GlobalStylesV2";
import {
	WebshopNavHeaderCategories,
	WebshopNavHeaderCategoriesDropdown,
	WebshopNavHeaderCategoriesDropdownItem,
	WebshopNavHeaderCategoriesShadow,
	WebshopNavHeaderContainer,
	WebshopNavHeaderFavouritesContainer,
	WebshopNavHeaderShoppingCartContainer,
	WebshopNavHeaderUniqueSellingPoint,
} from "./styles/webshop-nav-header.styled";

const WEBSHOP_CART_COUNT = "anwb-webshop-cart-item-count";

type Props = {
	categories: Array<ProductCategory>;
	categoryTreeOpen?: boolean;
	hideShoppingCartIcon?: boolean;
	totalCartQuantity?: number;
};

export function WebshopNavHeader({ categories, totalCartQuantity }: Props) {
	const { cartQuantity } = useCartQuantity();

	const totalQuantity = totalCartQuantity ?? cartQuantity;
	const [quantity, setQuantity] = useState<number>(totalQuantity);

	useEffect(() => {
		const checkQuantity = (): void => {
			const localQuantity = Number(getLocalStorage(WEBSHOP_CART_COUNT));
			if (!isNaN(localQuantity)) setQuantity(localQuantity);
		};
		const checkQuantityMessage = (event: MessageEvent): void => {
			if (event.data.type === CHANGE_CART_MESSAGE_TYPE) {
				setQuantity(event?.data?.count || 0);
			}
		};

		checkQuantity();

		window.addEventListener("storage", checkQuantity);
		window.addEventListener("message", checkQuantityMessage, false);
		return () => {
			window.removeEventListener("storage", checkQuantity);
			window.removeEventListener("message", checkQuantityMessage);
		};
	}, []);

	useEffect(() => {
		window.postMessage({
			count: totalQuantity,
			type: CHANGE_CART_MESSAGE_TYPE,
		});
		setQuantity(totalQuantity);
	}, [totalQuantity]);

	return (
		<>
			<GlobalStyle />

			<WebshopNavHeaderContainer>
				<nav role="navigation">
					<WebshopNavHeaderCategories
						id="nav-header-dropdown-menu"
						tabIndex={0}
					>
						<WebshopNavHeaderCategoriesShadow />
						<ComponentTitle as="h2">
							<Icon variant="menu" />
							Categorieën
						</ComponentTitle>
						<WebshopNavHeaderCategoriesDropdown>
							{categories
								.filter((category: ProductCategory) => category.includeInMenu)
								.map((category) => (
									<WebshopNavHeaderCategoriesDropdownItem key={category.id}>
										<ButtonLink as="a" href={category.targetUrl}>
											{category.displayName}
										</ButtonLink>
									</WebshopNavHeaderCategoriesDropdownItem>
								))}
						</WebshopNavHeaderCategoriesDropdown>
					</WebshopNavHeaderCategories>
					<SearchContainer>
						<SearchBox />
					</SearchContainer>
					<WebshopNavHeaderUniqueSellingPoint>
						<Icon variant="check-usp" />
						<BodyText as="span">
							Voor leden: Gratis verzending vanaf 50,-
						</BodyText>
					</WebshopNavHeaderUniqueSellingPoint>
					<WebshopNavHeaderFavouritesContainer>
						<Icon
							aria-label="Ga naar mijn favorieten"
							aria-role="button"
							onClick={() =>
								(window.location.href = "/mijn-anwb/mijn-favorieten")
							}
							size="lg"
							variant="favourite-outline"
						/>
					</WebshopNavHeaderFavouritesContainer>
					<WebshopNavHeaderShoppingCartContainer visible={false}>
						<ShoppingCartWidget quantity={quantity} />
					</WebshopNavHeaderShoppingCartContainer>
				</nav>
			</WebshopNavHeaderContainer>
		</>
	);
}

export default WebshopNavHeader;

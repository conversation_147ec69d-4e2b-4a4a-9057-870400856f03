import { createEnv } from "@t3-oss/env-core";
import { process } from "std-env";
import { z } from "zod";

export const env = createEnv({
	runtimeEnvStrict: {
		PROXY_HOST: process.env.PROXY_HOST,
		PROXY_PORT: process.env.PROXY_PORT,
		WEBSHOP_PRISMIC_ACCESS_TOKEN: process.env.WEBSHOP_PRISMIC_ACCESS_TOKEN,
	},
	server: {
		PROXY_HOST: z.string().optional(),
		PROXY_PORT: z.coerce.number().optional(),
		WEBSHOP_PRISMIC_ACCESS_TOKEN: z.string().min(1),
	},
});
